<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useBaseUrlList } from '@/hooks/common/useBaseUrlList'
import { networkManager } from '@/utils/networkManager'

const { baseUrlMsgListIntApi } = useBaseUrlList()
const { newInfoStepPage } = useNewInfoAll()
const { getUserIsLogin } = useUserInfo()

const executeStartupTasks = async () => {
  await baseUrlMsgListIntApi()
  if (getUserIsLogin.value) {
    console.log('getUserIsLogin.value====222', getUserIsLogin.value)
    newInfoStepPage(false)
  }
}

const handlePrivacyPolicy = () => {
  // #ifdef APP-PLUS
  setTimeout(() => {
    plus.navigator.closeSplashscreen()
    try {
      if (plus?.runtime && !plus.runtime.isAgreePrivacy() && plus.os.name === 'Android') {
        uni.redirectTo({
          url: '/setting/tourist/index',
        })
      }
    } catch (error) {
      console.log('处理隐私协议时出错', error)
    }
  }, 3000)
  // #endif
}

onLaunch(async () => {
  networkManager.startListening()
  const isOnline = await networkManager.checkNetworkStatus()
  if (isOnline) {
    try {
      await executeStartupTasks()
    } catch (error) {
      networkManager.addRetryTask(executeStartupTasks)
    }
  } else {
    networkManager.addPendingTask(async () => {
      await executeStartupTasks()
    })
  }
  handlePrivacyPolicy()
  console.log('App Launch')
})

onShow(() => {
  console.log('App Show')
  // 应用回到前台时检查网络状态
  networkManager.checkNetworkStatus()
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 每个页面公共css */
@import 'style/reset';
@import 'style/common';
@import 'style/iconfont-weapp-icon.css';
</style>
