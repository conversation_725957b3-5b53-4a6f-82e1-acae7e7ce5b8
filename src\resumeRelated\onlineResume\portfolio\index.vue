<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="作品集上传">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-card">
        <view class="flex-c border-b p-b-40rpx p-t-20rpx">
          <view class="mainText m-r-10rpx">作品URL</view>
          <wd-input
            class="flex-1"
            no-border
            v-model="fromData.url"
            border="none"
            custom-input-class="custom-class"
            placeholder="请输入作品集名称"
          ></wd-input>
        </view>
        <!-- /src/resumeRelated/img/pdfimg.png -->
        <view style="margin: auto" custom-class="custom-class-1" accept="image">
          <view class="m-t-40rpx img-upload">
            <template v-if="attachmentUrlIsshow">
              <view class="img-upload-icon active-bg"></view>
              <view class="pdf-mask" @click.stop="previewPDF">
                <view class="active-bg img-upload-icon"></view>
                <view class="close-btn" @click.stop="removeAttachment">✕</view>
              </view>
            </template>
            <template v-else-if="isPortfolio && !attachmentUrlIsshow">
              <view class="img-upload-icon"></view>
              <view class="pdf-mask" @click="uploadPDF">
                <view class="active-bg img-upload-icon"></view>
              </view>
            </template>
            <template v-else-if="isAddpdf && !attachmentUrlIsshow && !isPortfolio">
              <view class="img-upload-icon no-active-bg" @click="uploadPDF"></view>
            </template>
          </view>

          <view class="text-c p-t-20rpx p-b-20rpx dark-color text-28rpx">
            作品集上传{{ isPortfolio ? '（已上传）' : '' }}
          </view>
        </view>
      </view>
      <view class="subText p-t-20rpx">备注：上传附件/作品集「PDF格式,不得大于5MB」</view>
    </view>

    <template #bottom>
      <view class="btn-fixed flex-c">
        <view class="btn_box" @click="addSubmit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { encryption } from '@/service/crypto'
import { resumeFileAdd, resumeFileUpdate, resumeFileUrl } from '@/interPost/resume'
import { baseUrlImgCommon, baseUrlPrever } from '@/interPost/img'
import { useMessage } from 'wot-design-uni'

import { useResumeStore } from '@/store'
const message = useMessage()
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const isAdd = ref('')
const fromData = ref({
  url: '',
  attachmentId: '',
  baseInfoId: '',
  status: 1,
  sortNo: 1,
  attachmentUrl: '',
})
const isAddpdf = ref(true)
const isPortfolio = ref(false)
const attachmentUrlIsshow = ref('')
// 初始化的数据
// 获取列表
const getList = async () => {
  const res = await resumeFileUrl()
  if (res.data.length > 0) {
    fromData.value = { ...fromData.value, ...res.data[0] }
    console.log(fromData.value, 'fromData.value')
    attachmentUrlIsshow.value = fromData.value.attachmentUrl
  }
  console.log(res, '获取列表')
}
// 上传pdf
const uploadPDF = () => {
  console.log('上传pdf')
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewUpload?type=onlineResume`,
  })
}
//
onLoad(async (options) => {
  await nextTick()
  console.log(options, 'options')
  isAdd.value = options.isAdd
  fromData.value.baseInfoId = options.id
  if (isAdd.value === 'edit') {
    await getList()
  }
})
// 监听上传成功事件

onShow(() => {
  if (resumeStore.portfolio) {
    attachmentUrlIsshow.value = ''
    isAddpdf.value = false
    fromData.value.attachmentUrl = ''
    isPortfolio.value = true
  }

  fromData.value.attachmentId = resumeStore.portfolio
    ? resumeStore.portfolio
    : fromData.value.attachmentId
  console.log(isPortfolio.value, 'isPortfolio.value')
  console.log(fromData.value, 'fromData.value')
  console.log(resumeStore.portfolio, 'resumeStore.portfolio')
  console.log(attachmentUrlIsshow.value, 'attachmentUrlIsshow.value')
})
onMounted(() => {
  uni.$on('refresh-portfolio', getList)
})
onUnmounted(() => {
  resumeStore.setPortfolio('')
  uni.$off('refresh-portfolio')
})
// 提交
const addSubmit = async () => {
  const res: any =
    isAdd.value === 'add'
      ? await resumeFileAdd({ ...fromData.value })
      : await resumeFileUpdate({ ...fromData.value })
  if (res.code === 0) {
    console.log(fromData.value, 'fromData.value')
    resumeStore.setPortfolio('')
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const back = () => {
  resumeStore.setPortfolio('')
  if (isAdd.value === 'add') {
    uni.navigateBack()
  } else {
    uni.navigateBack()
  }
}
const removeAttachment = () => {
  attachmentUrlIsshow.value = ''
  isAddpdf.value = true
}

const previewPDF = () => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewpdf?fileUrl=${fromData.value.attachmentUrl}`,
  })
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  text-align: right;
  background-color: transparent;
}
::v-deep .custom-class {
  font-size: 30rpx;
  color: #000;
  text-align: right;
}
::v-deep .custom-class-1 {
  display: flex;
  justify-content: center;
  width: 400rpx !important;
  height: 320rpx !important;
  padding: 20rpx 0rpx 20rpx;
  margin: auto;
}
::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  object-fit: contain !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}
.no-active-bg {
  background-image: url('@/resumeRelated/img/zhengshuyanzhen_1.png');
}
.active-bg {
  background-image: url('@/resumeRelated/img/pdfimg.png');
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
.pdf-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  .close-btn {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    z-index: 3;
    font-size: 30rpx;
    color: #fff;
    cursor: pointer;
  }
  span {
    z-index: 2;
  }
}
.pdf-mask1 {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  cursor: pointer;
  // background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  .close-btn {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    z-index: 3;
    font-size: 30rpx;
    color: #fff;
    cursor: pointer;
  }
  span {
    z-index: 2;
  }
}
.setting {
  padding: 40rpx 40rpx;

  .setting-card {
    padding: 20rpx 30rpx 60rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .img-upload {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 280rpx;
      height: 160rpx;
      margin: 40rpx auto 0rpx;
      background-color: #ededed;
      border-radius: 20rpx;

      .img-upload-icon {
        width: 96rpx;
        height: 96rpx;

        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
