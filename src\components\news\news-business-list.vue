<template>
  <view class="flex flex-col gap-34rpx">
    <view
      v-for="(item, key) in displayList"
      :key="`news-list-${key}`"
      class="flex items-center gap-20rpx relative"
      @click="handleItemClick(item.conversationId)"
      @longpress="handleLongPress(key)"
      @touchstart="recordTouchStart"
      @touchmove="checkTouchMove"
    >
      <view :class="item?.isOnline ? 'border-twinkle bg_left_icon_box' : ''" class="size-92rpx">
        <wd-img :src="item?.avatar" height="100%" round width="100%" />
      </view>
      <view class="flex flex-col gap-16rpx flex-1 border-b border-#F0F0F0 pb-14rpx">
        <view class="flex items-center gap-4rpx">
          <view class="flex-1 flex items-center">
            <text class="c-#333333 text-28rpx font-500 line-clamp-1">
              {{ item?.nickname }}
            </text>
          </view>
          <view class="flex items-center gap-10rpx">
            <text class="c-#888888 text-24rpx">
              {{
                formatSmartTime(
                  item.lastMessage && 'time' in item.lastMessage ? item.lastMessage.time : '',
                )
              }}
            </text>
            <view v-if="!!item.unReadCount" class="bg-#FF3333 size-32rpx center border-rd-50%">
              <text class="text-20rpx c-#ffffff">
                {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
              </text>
            </view>
          </view>
        </view>
        <text
          :class="[
            'line-clamp-1 c-#888888 text-24rpx flex-1',
            { 'ml--8rpx': hasStatusPrefix(item.lastMessage as any) },
          ]"
        >
          {{ getLastTypeMessage(item.lastMessage as any) }}
        </text>
      </view>
      <view
        v-if="activeMenuIndex === key"
        class="absolute top-90% left-120rpx z-100 min-w-160rpx overflow-hidden bg-white border-rd-16rpx shadow-lg"
        @click.stop
      >
        <view
          v-for="(menuItem, index) in getMenuItems(item)"
          :key="menuItem.key"
          class="flex items-center justify-center h-88rpx px-32rpx transition-colors duration-200 active:bg-#f8f8f8"
          :class="[
            { 'border-b border-#f0f0f0': index < getMenuItems(item).length - 1 },
            menuItem.className,
          ]"
          @click="handleMenuAction(menuItem.key, item)"
        >
          <text class="text-28rpx font-400 c-#ff3b30">
            {{ menuItem.label }}
          </text>
        </view>
      </view>
    </view>
  </view>
  <view
    v-if="activeMenuIndex !== -1"
    class="fixed inset-0 z-99 bg-transparent"
    @click="closeMenu"
  ></view>
</template>

<script lang="ts" setup>
import { CONVERSATION_MARKS } from '@/enum'
import { formatSmartTime } from '@/utils'
import type { ConversationWithUserInfo } from '@/hooks/common/useIMConversation'

type ConversationMarkType = (typeof CONVERSATION_MARKS)[keyof typeof CONVERSATION_MARKS]

interface MenuItem {
  key: string
  label: string
  className?: string
  visible?: (item: ConversationWithUserInfo) => boolean
}

interface propsInt {
  type: ConversationMarkType
}
const props = withDefaults(defineProps<propsInt>(), {
  type: CONVERSATION_MARKS.ALL,
})
const searchModel = defineModel<string>('search', {
  default: '',
  type: String,
})
const $emits = defineEmits<{
  (e: 'chat', id: string): void
}>()
const { conversationList, getLastTypeMessage, getConversationsByMark, deleteConversation } =
  useIMConversation()

const menuConfig: MenuItem[] = [
  {
    key: 'delete',
    label: '删除',
    className: 'menu-item-danger',
    visible: () => true,
  },
]
// 菜单状态管理
const activeMenuIndex = ref(-1)
// 简化的触摸状态
const touchStartPos = ref({ x: 0, y: 0 })
const isTouchMoved = ref(false)
const markConversations = ref<ConversationWithUserInfo[]>([])

const displayList = computed(() => {
  let list =
    props.type === CONVERSATION_MARKS.ALL ? conversationList.value : markConversations.value
  if (searchModel.value.trim()) {
    list = list.filter((item) =>
      item?.nickname?.toLowerCase().includes(searchModel.value.toLowerCase()),
    )
  }
  return list
})

const loadMarkConversations = async () => {
  if (props.type === CONVERSATION_MARKS.ALL) return
  try {
    const conversations = await getConversationsByMark(props.type)
    markConversations.value = conversations
  } catch (error) {
    markConversations.value = []
  }
}

const toChatPage = (id: string) => {
  $emits('chat', id)
}
// 检查消息是否包含状态前缀
const hasStatusPrefix = (lastMessage: any) => {
  if (!lastMessage) {
    return false
  }
  const messageContent = getLastTypeMessage(lastMessage)
  const hasPrefix = messageContent.startsWith('「')
  return hasPrefix
}

const recordTouchStart = (event: TouchEvent) => {
  const touch = event.touches[0]
  touchStartPos.value = { x: touch.clientX, y: touch.clientY }
  isTouchMoved.value = false
}

const checkTouchMove = (event: TouchEvent) => {
  const touch = event.touches[0]
  const deltaX = Math.abs(touch.clientX - touchStartPos.value.x)
  const deltaY = Math.abs(touch.clientY - touchStartPos.value.y)

  if (deltaX > 10 || deltaY > 10) {
    isTouchMoved.value = true
  }
}

const getMenuItems = (item: ConversationWithUserInfo): MenuItem[] => {
  return menuConfig.filter((menuItem) => !menuItem.visible || menuItem.visible(item))
}

// 处理长按事件
const handleLongPress = (index: number) => {
  if (isTouchMoved.value) return
  uni.vibrateShort()
  activeMenuIndex.value = index
}

const handleItemClick = (conversationId: string) => {
  if (activeMenuIndex.value !== -1) {
    closeMenu()
    return
  }
  toChatPage(conversationId)
}
const closeMenu = () => {
  activeMenuIndex.value = -1
}

const handleMenuAction = (action: string, item: ConversationWithUserInfo) => {
  closeMenu()
  switch (action) {
    case 'delete':
      deleteConversation(item.conversationId)
      break
    default:
      console.log('Unknown action:', action)
  }
}

watch(() => props.type, loadMarkConversations, { immediate: true })
watch(
  conversationList,
  () => {
    if (props.type !== CONVERSATION_MARKS.ALL) {
      loadMarkConversations()
    }
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 6s infinite;
  }
}

.bg_left_icon_box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86rpx;
  height: 86rpx;
  border: 3rpx solid #0ea500;
  border-radius: 50rpx;
}

@keyframes twinkle {
  0%,
  100% {
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
  }
  50% {
    background: linear-gradient(45deg, #00ff00, #0ea500, #00ff00);
  }
}
</style>
