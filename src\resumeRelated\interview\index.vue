<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="待面试">
      <template #right>
        <view class="text-28rpx c-#000" @click="goHistory">历史面试</view>
      </template>
    </CustomNavBar>
    <z-paging
      ref="pagingRef"
      v-model="pageData"
      :fixed="false"
      :paging-style="pageStyle"
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
      safe-area-inset-bottom
      @query="queryList"
    >
      <view class="pageList">
        <view v-if="pageData.length > 0" class="c-#888 text-32rpx p-b-20rpx p-l-40rpx"></view>
        <view v-for="(item, index) in pageData" :key="index">
          <InterviewCard
            :item="item"
            :show-time="shouldShowTime(index)"
            :show-date="shouldShowDate(index)"
          />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryWaitMeetingList } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { getCustomBar } from '@/utils/storage'
import InterviewCard from '@/resumeRelated/component/interviewCard.vue'
import { truncateText } from '@/utils/util'
import { valueOfToDate } from '@/utils'
const { pagingRef, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '50rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '40rpx',
  },
})
const customBar = ref(null)

// 判断是否显示时间
const shouldShowTime = (index: number) => {
  const currentItem = pageData.value[index]
  if (!currentItem?.agreeTime) return true

  const currentTime = new Date(currentItem.agreeTime)
  const currentDate = currentTime.toISOString().split('T')[0] // YYYY-MM-DD
  const currentHour = currentTime.getHours()
  const currentMinute = currentTime.getMinutes()

  // 检查当前时间点是否在之前的项目中已经显示过
  for (let i = 0; i < index; i++) {
    const prevItem = pageData.value[i]
    if (!prevItem?.agreeTime) continue

    const prevTime = new Date(prevItem.agreeTime)
    const prevDate = prevTime.toISOString().split('T')[0]
    const prevHour = prevTime.getHours()
    const prevMinute = prevTime.getMinutes()

    // 如果找到相同的日期和时分（不判断秒），则不显示
    if (currentDate === prevDate && currentHour === prevHour && currentMinute === prevMinute) {
      return false
    }
  }

  return true
}

// 判断是否显示日期
const shouldShowDate = (index: number) => {
  const currentItem = pageData.value[index]
  if (!currentItem?.agreeTime) return true

  const currentTime = new Date(currentItem.agreeTime)
  const currentDate = currentTime.toISOString().split('T')[0] // YYYY-MM-DD

  // 检查当前日期是否在之前的项目中已经显示过
  for (let i = 0; i < index; i++) {
    const prevItem = pageData.value[i]
    if (!prevItem?.agreeTime) continue

    const prevTime = new Date(prevItem.agreeTime)
    const prevDate = prevTime.toISOString().split('T')[0]

    // 如果找到相同的日期，则不显示
    if (currentDate === prevDate) {
      return false
    }
  }

  return true
}

// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await queryWaitMeetingList()
  if (res.code === 0) {
    res.data &&
      res.data.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryStart =
          ele.workSalaryStart === 0 ? '面议' : numberTokw(ele.workSalaryStart + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
        if (!ele.headImgUrl) {
          ele.headImgUrl =
            ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
        }
      })

    pagingRef.value.complete(res.data)
  }
}
uni.$on('refreshInterviewListToPerson', () => {
  pagingRef.value.reload()
})
onUnload(async () => {
  uni.$off('refreshInterviewListToPerson')
})
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  await pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}

.pageList-right {
  border-left: 1rpx solid #cccaca;
}

.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  background: #4399ff;
  border-radius: 20rpx;
}
</style>
