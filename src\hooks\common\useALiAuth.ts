import { CommonUtil } from 'wot-design-uni'
import { loginGetMobileLogin } from '@/service/login'
import { setCheckInfo } from '@/utils/storage'

const ANDROID_LOGIN_KEY = import.meta.env.VITE_ANDROID_LOGIN_KEY
const IOS_LOGIN_KEY = import.meta.env.VITE_IOS_LOGIN_KEY

export const useALiAuth = () => {
  const aLiSdk = ref<AnyObject>({})
  // #ifdef APP-PLUS
  aLiSdk.value = uni.requireNativePlugin('AliCloud-NirvanaPns')
  // #endif
  const { setUserIntel } = useUserInfo()
  const { newInfoStepPage } = useNewInfoAll()

  const aLiAuthInit = async () => {
    const { sysAppPlatform, sysInfo } = useSystemInfo()
    aLiSdk.value?.setAuthSDKInfo?.(
      sysAppPlatform.value === 'android' ? ANDROID_LOGIN_KEY : IOS_LOGIN_KEY,
    )
    const config = {
      uiConfig: {
        // === 全局设置 ===
        globalFontName: 'PingFangSC-Regular',
        setPrivacyAlertIsNeedShow: false,
        setStatusBarStyle: '1',
        setNavHidden: 'false',
        setLogoHidden: 'false',
        setSloganHidden: 'false',
        setSwitchHidden: 'false',
        setCheckboxHidden: 'false',

        // === 弹窗样式设置 - 全屏显示 ===
        setDialogTheme: {
          alpha: '0.5',
          isBottom: 'false',
          offsetX: '0',
          offsetY: '0',
          width: '330',
          height: '500',
        },

        // === 背景设置 - 纯色背景 ===
        setBackgroundUi: {
          backgroundColor: '#FFFFFF', // 白色背景
          imagePath: '',
          imageUrl: '',
          gifPath: '',
          gifUrl: '',
          videoPath: '',
          videoUrl: '',
          webviewPath: '',
          webviewUrl: '',
          webviewScrollEnabled: '',
          weexJsPath: '',
          weexJsUrl: '',
        },

        // === 导航栏设置 ===
        setNavUi: {
          bgColor: '#FFFFFF', // 白色导航栏
          text: '一键登录',
          textColor: '#333333', // 深灰色文字
          textSize: '18',
          returnImgHidden: 'false',
          returnImgPath: 'static/app/close.png',
          returnImgWidth: '44',
          returnImgHeight: '44',
          suspendDisMissVC: false,
        },

        // === Logo设置 ===
        setLogoUi: {
          imgPath: 'static/app/logo.png',
          top: '30',
          width: '80',
          height: '80',
        },

        // === 标语设置 ===
        setSloganUi: {
          text: '使用本机号码一键登录',
          textColor: '#666666',
          textSize: '14',
          top: '150',
        },

        // === 手机号掩码设置 ===
        setNumberUi: {
          textColor: '#333333',
          textSize: '20',
          left: '0',
          top: '200',
        },

        // === 登录按钮设置 ===
        setLoginBtnUi: {
          text: '一键登录',
          textColor: '#FFFFFF',
          textSize: '16',
          imgPath: '',
          color: '#007AFF',
          activeImgPath: '',
          invalidImgPath: '',
          hightedImgPath: '',
          top: '250',
          width: '250',
          height: '44',
        },

        // === 切换登录方式按钮设置 ===
        setSwitchUi: {
          text: '其他登录方式',
          textColor: '#007AFF',
          textSize: '14',
          top: '400',
        },

        // === 隐私协议勾选框设置 ===
        setCheckBoxUi: {
          defaultChecked: 'false', // 默认不勾选，用户主动同意
          unCheckedImgPath: '', // 使用系统默认
          checkedImgPath: '',
          width: '16',
          checkBoxPostion: 'top',
        },
        // === 自定义协议配置 ===
        setAppPrivacyOne: {
          title: '《用户协议》',
          url: 'https://www.easyzhipin.com/#/agreementRules',
        },
        setAppPrivacyTwo: {
          title: '《隐私政策》',
          url: 'https://www.easyzhipin.com/#/privacyPolicy',
        },
        // === 隐私协议文案设置 ===
        setPrivacyUi: {
          beforeText: '登录即同意',
          endText: '',
          baseColor: '#666666',
          protocolColor: '#007AFF',
          operatorColor: '#007AFF',
          oneColor: '#007AFF',
          twoColor: '#007AFF',
          threeColor: '#007AFF',
          textSize: '12',
          vendorPrivacyPrefix: '《',
          vendorPrivacySuffix: '》',
          conectTexts: "['和','、','、']",
          operatorIndex: '0',
          bottom: '100',
          top: '0',
          marginLR: '40',
          alignment: '1', // 居中对齐
          expandAuthPageCheckedScope: true,
        },
        // === 协议详情页导航栏设置 ===
        setWebNavUi: {
          bgColor: '#FFFFFF',
          textColor: '#333333',
          textSize: '18',
          returnImgPath: 'static/app/back.png',
        },
      },
      // === 移除自定义控件，使用默认布局 ===
      widgets: [],
      // === 移除隐私弹窗自定义控件 ===
      privacyAlertWidgets: [],
    }
    aLiSdk.value?.getLoginToken?.(
      5000,
      config,
      async (tokenResult) => {
        console.log('tokenResult===========进入', tokenResult)
        const { token: accessToken } = tokenResult
        if (
          tokenResult.resultCode === '600000' ||
          tokenResult.resultCode === '600001' ||
          tokenResult.resultCode === '600002' ||
          tokenResult.resultCode === '600003'
        ) {
          console.log('accessToken===========成功', accessToken)
          try {
            const { data } = await loginGetMobileLogin(
              {
                accessToken,
                outId: CommonUtil.uuid(),
                deviceInfo: sysInfo,
                deviceModel: sysInfo.deviceModel,
                deviceSystem: sysInfo.osName,
                deviceType: sysInfo.osVersion,
              },
              {
                custom: {
                  catch: true,
                },
              },
            )
            console.log('data===========登陆接口', data)
            const { requiredFinishStatus = 0 } = data
            setUserIntel(data)
            aLiSdk.value.quitLoginPage()
            if (data?.type !== null && data?.type !== undefined) {
              console.log('data?.type===========登陆小步骤', data?.type)
              newInfoStepPage(false, requiredFinishStatus)
            } else {
              setCheckInfo({})
              uni.reLaunch({
                url: '/loginSetting/category/index',
              })
            }
          } catch (error) {
            console.log('error===========登陆接口失败', error)
            const errMsg = error?.msg || '登录失败，请稍后重试'
            plus.nativeUI.toast(errMsg, {
              verticalAlign: 'top',
            })
            aLiSdk.value.quitLoginPage()
          }
        }
      },
      (clickResult) => {
        console.log('clickResult', clickResult)
        const { resultCode, result } = clickResult
        switch (resultCode) {
          case '700002':
            if (!result?.isChecked) {
              plus.nativeUI.toast('请先同意相关协议', {
                verticalAlign: 'top',
              })
            }
            break
        }
      },
    )
  }

  return {
    aLiAuthInit,
  }
}
