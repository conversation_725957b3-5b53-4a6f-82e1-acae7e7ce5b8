<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="简历预览" class="base_header">
        <template #right>
          <view class="text-28rpx font-w-500 c-#333" @click="downloadPdf">下载</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="w-100">
      <web-view :src="link" :fullscreen="false"></web-view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { ref, onMounted, nextTick, getCurrentInstance } from 'vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const fileName = ref('')
const link = ref('')
const pdfUrl = ref('')
onLoad((options) => {
  fileName.value = options.options
  if (options.fileUrl) {
    pdfUrl.value = options.fileUrl
    link.value = '/static/hybrid/pdf.html?url=' + encodeURIComponent(options.fileUrl)
  }
})
// 下载pdf
const downloadPdf = () => {
  console.log(pdfUrl.value)
  uni.downloadFile({
    url: pdfUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        // 2. 保存到本地文件系统
        uni.saveFile({
          tempFilePath: res.tempFilePath,
          success: (saveRes) => {
            const savedFilePath = saveRes.savedFilePath
            // 3. 提示用户保存路径
            uni.showToast({
              title: 'PDF保存成功',
              icon: 'success',
            })
            setTimeout(() => {
              // 打开文档查看
              uni.openDocument({
                filePath: savedFilePath,
                success: function (res) {
                  // console.log('打开文档成功');
                },
              })
            }, 3000)
          },
          fail: (err) => {
            console.error('保存失败:', err)
            uni.showToast({ title: '保存失败', icon: 'none' })
          },
        })
      }
    },
  })
}
onMounted(() => {
  const instance = getCurrentInstance()
  const query = uni.createSelectorQuery().in(instance)
  const { windowHeight } = uni.getSystemInfoSync() // 屏幕高度（单位：px）
  console.log('屏幕高度:', windowHeight)
  if (instance && instance.proxy) {
    const currentWebview = instance.proxy.$scope?.$getAppWebview()
    if (currentWebview) {
      nextTick(() => {
        setTimeout(() => {
          const closeHeight = 0
          let baseHeaderHeight = 0

          query
            .select('.base_header')
            .boundingClientRect((res) => {
              const rect = Array.isArray(res) ? res[0] : res
              if (rect && rect.height) {
                baseHeaderHeight = rect.height
              } else {
                baseHeaderHeight = 100 // 默认高度
              }
            })
            .exec(() => {
              const totalTop = closeHeight + baseHeaderHeight
              console.log('Calculated totalTop:', totalTop)

              const wv = currentWebview.children()?.[0]
              if (wv) {
                wv.setStyle({
                  top: `${totalTop}px`,
                  height: `${windowHeight - totalTop + 30}px`,
                  zIndex: -1,
                })
              }
            })
        }, 300)
      })
    }
  }
})
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}
.w-100 {
  width: 100vw;
  height: 100vh;
  min-height: 400px;
  background: #fff;
}
</style>
