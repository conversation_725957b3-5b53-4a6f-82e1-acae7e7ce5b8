import type { PermissionType } from '@/types/permission'

export interface PermissionTipConfig {
  title: string
  content: string
  duration?: number
}

export const PERMISSION_TIP_CONFIG: Record<PermissionType, PermissionTipConfig> = {
  location: {
    title: '位置权限使用说明：',
    content:
      '当您使用“附近岗位推荐”“位置导航”等功能时，需获取定位，可以快速匹配就近工作机会。提升招聘求职效率。',
    duration: 3000,
  },
  camera: {
    title: '相机权限使用说明：',
    content:
      '当您需要拍摄个人头像、上传证件照片或补充求职材料时，我们需要获取相机权限，帮助您完善招聘求职信息。',
    duration: 3000,
  },
  photoLibrary: {
    title: '相册权限使用说明：',
    content:
      '需要申请读取媒体图片的权限，当您需要上传头像或其他图片证明材料时，可便捷从相册选取已拍摄完成的照片。',
    duration: 3000,
  },
  record: {
    title: '录音权限使用说明：',
    content: '用于语音录制等功能。',
    duration: 3000,
  },
  push: {
    title: '通知权限使用说明：',
    content: '用于及时向您推送重要信息。',
    duration: 3000,
  },
  contact: {
    title: '通讯录权限使用说明：',
    content: '用于联系人相关功能。',
    duration: 3000,
  },
  calendar: {
    title: '日历权限使用说明：',
    content: '用于日程管理功能。',
    duration: 3000,
  },
  memo: {
    title: '备忘录权限使用说明：',
    content: '用于笔记同步功能。',
    duration: 3000,
  },
}

class PermissionTipManager {
  private currentTip: PlusNativeObjView | null = null
  private tipId = 'permission-tip-view'

  /**
   * 手动分割文本为多行
   */
  private splitTextToLines(text: string, maxCharsPerLine: number): string[] {
    const lines: string[] = []
    let currentLine = ''

    for (let i = 0; i < text.length; i++) {
      const char = text[i]

      // 如果当前行加上新字符会超出限制，就换行
      if (currentLine.length >= maxCharsPerLine) {
        lines.push(currentLine)
        currentLine = char
      } else {
        currentLine += char
      }
    }

    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine)
    }

    return lines
  }

  /**
   * 显示权限提示
   */
  showPermissionTip(
    permissionType: PermissionType,
    config?: Partial<PermissionTipConfig>,
  ): Promise<void> {
    return new Promise((resolve) => {
      if (this.currentTip) {
        this.hideTip()
      }

      const tipConfig = { ...PERMISSION_TIP_CONFIG[permissionType], ...config }
      const systemInfo = uni.getSystemInfoSync()
      const statusBarHeight = systemInfo.statusBarHeight || 20
      const screenWidth = systemInfo.screenWidth

      // 计算每行最大字符数（粗略估算）
      const maxCharsPerLine = Math.floor((screenWidth - 24 - 32) / 14) // 减去边距，除以字体宽度
      const contentLines = this.splitTextToLines(tipConfig.content, maxCharsPerLine)

      // 动态计算高度
      const lineHeight = 20
      const titleHeight = 20
      const padding = 24
      const dynamicHeight = titleHeight + contentLines.length * lineHeight + padding

      this.currentTip = new plus.nativeObj.View(this.tipId, {
        top: `${statusBarHeight + 10}px`,
        left: '16px',
        width: `${screenWidth - 32}px`,
        height: `${Math.max(70, dynamicHeight)}px`,
        backgroundColor: 'rgba(0,0,0,0)',
      })

      // 绘制主体背景（完全一致的尺寸）
      this.currentTip.drawRect(
        {
          color: '#ffffff',
          radius: '16px',
        },
        {
          top: '0px',
          left: '0px',
          width: '100%',
          height: '100%',
        },
      )

      // 绘制标题
      this.currentTip.drawText(
        tipConfig.title,
        {
          top: '16px',
          left: '20px',
          width: 'calc(100% - 40px)',
          height: '20px',
        },
        {
          size: '15px',
          color: '#1A1A1A',
          weight: 'bold',
          align: 'left',
        },
      )

      // 绘制多行内容
      contentLines.forEach((line, index) => {
        this.currentTip?.drawText(
          line,
          {
            top: `${40 + index * lineHeight}px`,
            left: '20px',
            width: 'calc(100% - 40px)',
            height: `${lineHeight}px`,
          },
          {
            size: '14px',
            color: '#4A4A4A',
            align: 'left',
          },
        )
      })

      this.currentTip.show()
      resolve()
    })
  }

  /**
   * 隐藏权限提示
   */
  hideTip(): void {
    if (this.currentTip) {
      this.currentTip.hide()
      this.currentTip.close()
      this.currentTip = null
    }
  }

  /**
   * 检查是否显示中
   */
  isShowing(): boolean {
    return this.currentTip !== null
  }
}

export const permissionTipManager = new PermissionTipManager()
