<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="工作经历">
        <template #left>
          <wd-icon class="back-button" color="#000" name="arrow-left" size="20" @click="back" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">公司名称</view>
          <!-- <view class="color-8 text-32rpx" @click="goCompany">请输入</view> -->
          <view
            :class="fromData.company ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx"
            @click="goCompany"
          >
            {{ fromData.company ? fromData.company : '请输入公司名称' }}
          </view>
        </view>
        <view class="icon-right" @click="goCompany">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <!-- 隐藏个人信息选项 -->
      <view
        v-if="fromData.company"
        class="hide-info-option flex-c m-t-20rpx"
        @click="handleSelected()"
      >
        <wd-img
          :height="16"
          :src="isShield ? selectedIcon : unSelectedIcon"
          :width="16"
          class="wd-img"
        />
        <text
          :class="isShield ? 'c-#075eff' : 'c-#9e9e9e'"
          class="hide-info-text text-24rpx m-l-10rpx"
        >
          对该公司隐藏我的个人信息
        </text>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">所在行业</view>
          <view
            :class="fromData.industry ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap"
            @click="goIndustry"
          >
            {{ fromData.industry ? fromData.industry : '请选择期望行业' }}
          </view>
        </view>
        <view class="icon-right" @click="goIndustry">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">在职时间</view>
          <view class="flex-c">
            <!-- <view class="color-8 text-32rpx w-40">开始时间</view> -->
            <wd-datetime-picker
              v-model="startTimeDisplay"
              :before-confirm="beforeConfirmStart"
              :default-value="defaultValue"
              :max-date="maxDate"
              :min-date="minDate"
              :zIndex="10001"
              class="flex-1 time-c"
              custom-value-class="custom-label-class"
              placeholder="开始时间"
              type="year-month"
            />
            <view class="w-20 c-#888888">至</view>
            <!-- <view class="color-8 text-32rpx w-30">结束时间</view> -->
            <wd-datetime-picker
              v-model="endTimeDisplay"
              :before-confirm="beforeConfirmEnd"
              :default-value="defaultValue"
              :maxDate="maxDate"
              :minDate="minDate"
              :zIndex="10001"
              class="flex-1 time-c"
              custom-value-class="custom-label-class"
              placeholder="结束时间"
              type="year-month"
            />
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between" @click="handWorkPosition">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">工作岗位</view>
          <view
            :class="fromData.positionName ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap single-line-ellipsis"
          >
            {{ fromData.positionName ? fromData.positionName : '请选择工作岗位' }}
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">工作内容</view>
          <!-- <view class="color-8 text-32rpx">请输入</view> -->
          <view
            :class="fromData.workDescription ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap single-line-ellipsis"
            @click="goWorkDescription"
          >
            {{ fromData.workDescription ? fromData.workDescription : '请输入工作内容' }}
          </view>
        </view>
        <view class="icon-right" @click="goWorkDescription">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
          ></wd-icon>
        </view>
      </view>
      <!-- <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">拥有技能</view>
          <view
            class="text-32rpx text-white-space"
            :class="fromData.skills ? 'c-#333333' : 'c-#888888'"
            @click="goSkills"
          >
            {{ fromData.skills ? fromData.skills : '非必填' }}
          </view>
        </view>
        <view class="icon-right" @click="goSkills">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->
      <!-- <view class="form-list flex-between" @click="handWorkPosition">
        <view class="form-list-item mainText">工作岗位</view>
        <view class="flex-1 flex-c flex-c-just">
          <view
            class="text-32rpx text-pre-wrap single-line-ellipsis"
            :class="fromData.workPerformance ? 'c-#333333' : 'c-#888888'"
          >
            {{ positionObj.expectedPositions ? positionObj.expectedPositions : '工作岗位' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->

      <!-- 岗位标签 - 只有当有四级标签时才展示 -->
      <!-- <view
        v-if="positionObj.fourthLevelPositions && positionObj.fourthLevelPositions.length > 0"
        class="form-list flex-between"
      >
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">岗位标签</view>
          <wd-picker
            v-model="fromData.positionMark"
            :columns="positionMarkColumns"
            placeholder="请选择岗位标签"
            custom-value-class="custom-label-class"
          />
        </view>
        <view class="icon-right">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText p-b-10rpx">工作业绩</view>
          <!-- <view class="color-8 text-32rpx">非必填</view> -->
          <view
            :class="fromData.workPerformance ? 'c-#333333' : 'c-#888888'"
            class="text-28rpx text-pre-wrap single-line-ellipsis"
            @click="goWorkPerformance"
          >
            {{ fromData.workPerformance ? fromData.workPerformance : '非必填' }}
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            class="arrow-right-icon"
            color="#888888"
            name="chevron-right"
            size="20px"
            @click="goWorkPerformance"
          ></wd-icon>
        </view>
      </view>
      <!--      <view class="form-list flex-between">-->
      <!--        <view class="form-list-item">-->
      <!--          <view class="mainText p-b-10rpx">所属部门</view>-->
      <!--          &lt;!&ndash; <view class="color-8 text-32rpx">非必填</view> &ndash;&gt;-->
      <!--          <view-->
      <!--            class="text-32rpx text-pre-wrap single-line-ellipsis"-->
      <!--            :class="fromData.department ? 'c-#333333' : 'c-#888888'"-->
      <!--            @click="goDept"-->
      <!--          >-->
      <!--            {{ fromData.department ? fromData.department : '非必填' }}-->
      <!--          </view>-->
      <!--        </view>-->
      <!--        <view class="icon-right">-->
      <!--          <wd-icon-->
      <!--            @click="goDept"-->
      <!--            name="chevron-right"-->
      <!--            size="20px"-->
      <!--            color="#888888"-->
      <!--            class="arrow-right-icon"-->
      <!--          ></wd-icon>-->
      <!--        </view>-->
      <!--      </view>-->
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          :class="isAdd === 'edit' ? 'w-30' : ''"
          class="btn-delet m-r-30rpx"
          @click="delWork"
        >
          删除
        </view>
        <view :class="isAdd === 'edit' ? 'w-70' : 'w-100'" class="btn_box">
          <view class="btn_bg" @click="addWork">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>
<script lang="ts" setup>
import isEqual from 'lodash/isEqual'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getCustomBar } from '@/utils/storage'
import { useLoginStore, useResumeStore } from '@/store'
import { formatTime } from '@/utils/common'
import {
  resumeWorkExperiencesDel,
  resumeWorkExperiencesAdd,
  resumeWorkExperiencesUpdate,
} from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
import unSelectedIcon from '@/resumeRelated/img/unSelected_icon.png'
import selectedIcon from '@/resumeRelated/img/selected_icon.png'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 是否是新增
const isAdd = ref('')
// 工作编辑
const objItem = ref()
// 公司
const resumeStore = useResumeStore()
// 公司id
const companyId = ref(null)
// 工作岗位
const positionObj = ref<AnyObject>({
  expectedPositions: '',
  expectedPositionsCode: '',
})

const isShield = ref<boolean>(false)

// 处理是否屏蔽当前公司
const handleSelected = () => {
  isShield.value = !isShield.value
}

// 岗位标签选项 - 计算属性
const positionMarkColumns = computed(() => {
  if (positionObj.value.fourthLevelPositions && positionObj.value.fourthLevelPositions.length > 0) {
    return positionObj.value.fourthLevelPositions.map((item: any) => item.name)
  }
  return []
})
// 行业
const jobName = ref('')
const customBar = ref(0)
const loginStore = useLoginStore()
const id = ref(null)
const message = useMessage()
// 表单
const fromData = ref({
  baseInfoId: null,
  startTime: null as number | null,
  endTime: null as number | null,
  department: '',
  company: '',
  workDescription: '',
  workPerformance: '',
  skills: '',
  industry: '',
  industryId: null,
  position: '',
  positionId: '',
  work: '',
  workId: '',
  positionCode: '',
  positionName: '',
  positionMark: '', // 岗位标签
  positionMarkCode: '', // 岗位标签代码
})
// 表单初始化 - 将在数据加载完成后设置
const fromDataInit = ref({})

// 时间限制：50年前到当天
const now = new Date()
const minDate = new Date(now.getFullYear() - 50, 0, 1).getTime() // 50年前的1月1日
const maxDate = new Date().getTime() // 当天
console.log('minDate', minDate)
console.log('maxDate', maxDate)
const defaultValue = new Date().getTime() // 默认值为当前月份
console.log('defaultValue', defaultValue)

// 时间显示值的计算属性（用于组件显示）
const startTimeDisplay = computed({
  get: () => {
    if (fromData.value.startTime) {
      // 如果是时间戳数字，直接转换为Date对象
      return new Date(fromData.value.startTime)
    }
    return null
  },
  set: (value: any) => {
    if (value) {
      // 处理不同类型的 value，存储为数字类型
      if (typeof value === 'number') {
        // 如果是时间戳数字
        fromData.value.startTime = value
      } else if (value instanceof Date) {
        // 如果是 Date 对象
        fromData.value.startTime = value.getTime()
      } else {
        // 其他情况，尝试转换
        fromData.value.startTime = new Date(value).getTime()
      }
    } else {
      fromData.value.startTime = null
    }
  },
})

const endTimeDisplay = computed({
  get: () => {
    if (fromData.value.endTime) {
      // 如果是时间戳数字，直接转换为Date对象
      return new Date(fromData.value.endTime)
    }
    return null
  },
  set: (value: any) => {
    if (value) {
      // 处理不同类型的 value，存储为数字类型
      if (typeof value === 'number') {
        // 如果是时间戳数字
        fromData.value.endTime = value
      } else if (value instanceof Date) {
        // 如果是 Date 对象
        fromData.value.endTime = value.getTime()
      } else {
        // 其他情况，尝试转换
        fromData.value.endTime = new Date(value).getTime()
      }
    } else {
      fromData.value.endTime = null
    }
  },
})
// 开始时间点击确认
const beforeConfirmStart = (value, resolve, picker) => {
  if (value > fromData.value.endTime && fromData.value.endTime) {
    uni.showToast({
      title: '结束时间不能小于开始时间',
      icon: 'none',
      duration: 3000,
    })
    resolve(false)
  } else {
    resolve(true)
  }
}
// 结束时间点击确认
const beforeConfirmEnd = (value, resolve, picker) => {
  if (fromData.value.startTime > value && fromData.value.startTime) {
    uni.showToast({
      title: '结束时间不能小于开始时间',
      icon: 'none',
      duration: 3000,
    })
    resolve(false)
  } else {
    resolve(true)
  }
}
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  id.value = options.id

  // // 设置默认行业信息
  // fromData.value.industry = '不限'
  // fromData.value.industryId = 0

  if (resumeStore.isAdd === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    const editData = JSON.parse(decodeURIComponent(options.item))
    fromData.value = { ...editData }

    // 如果编辑模式下的时间不是时间戳格式，需要转换为数字
    if (fromData.value.startTime) {
      if (typeof fromData.value.startTime === 'string') {
        fromData.value.startTime = new Date(fromData.value.startTime).getTime()
      }
    }
    if (fromData.value.endTime) {
      if (typeof fromData.value.endTime === 'string') {
        fromData.value.endTime = new Date(fromData.value.endTime).getTime()
      }
    }
    // // 编辑模式下，同步岗位数据
    // if (editData.positionName) {
    //   positionObj.value.expectedPositions = editData.positionName
    //   positionObj.value.expectedPositionsCode = editData.positionCode || ''
    //   // 同步到loginStore以便岗位选择页面能正确回显
    //   loginStore.setpositionData({
    //     expectedPositions: editData.positionName,
    //     expectedPositionsCode: editData.positionCode || '',
    //   })
    // }

    // // 编辑模式下，同步岗位标签数据
    // if (editData.positionMark) {
    //   fromData.value.positionMark = editData.positionMark
    //   fromData.value.positionMarkCode = editData.positionMarkCode || ''
    // }
  }

  // 在所有数据加载完成后，设置初始化数据用于比较
  await nextTick()
  fromDataInit.value = JSON.parse(JSON.stringify(fromData.value))
})
// 监听数据是否有变化
onShow(async () => {
  await nextTick()
  isAdd.value = resumeStore.isAdd
  companyId.value = resumeStore.companyId
  fromData.value.department = resumeStore.department
  fromData.value.company = resumeStore.company
  fromData.value.workDescription = resumeStore.workDescription
  fromData.value.skills = resumeStore.skills
  fromData.value.workPerformance = resumeStore.workPerformance
  fromData.value.industry = loginStore.industryObj.industryName
  fromData.value.industryId = Number(loginStore.industryObj.industryCode)
  console.log(loginStore.positionObj, 'loginStore.positionObj=====hah')
  fromData.value.positionName = loginStore.positionObj.expectedPositions
  fromData.value.positionCode = Number(loginStore.positionObj.expectedPositionsCode)
})

// 监听岗位标签选择变化
// watch(
//   () => fromData.value.positionMark,
//   (newMark) => {
//     if (newMark && positionObj.value.fourthLevelPositions) {
//       const selectedMarkObj = positionObj.value.fourthLevelPositions.find(
//         (item: any) => item.name === newMark,
//       )
//       if (selectedMarkObj) {
//         fromData.value.positionMarkCode = selectedMarkObj.code
//       }
//     } else {
//       fromData.value.positionMarkCode = ''
//     }
//   },
// )
onUnload(() => {
  loginStore.setindustryObj({})
  // 清理岗位数据
  loginStore.setpositionData({})
})
// 返回
const back = () => {
  if (resumeStore.isAdd === 'add') {
    console.log(fromData.value, fromDataInit.value)
    if (
      fromData.value.company !== '' ||
      fromData.value.workDescription !== '' ||
      fromData.value.workPerformance !== '' ||
      fromData.value.industry !== '' ||
      fromData.value.positionName !== '' ||
      fromData.value.startTime !== null ||
      fromData.value.endTime !== null
    ) {
      resumeStore.setDepartment('')
      resumeStore.setCompany('')
      resumeStore.setWorkDescription('')
      resumeStore.setSkills('')
      resumeStore.setWorkPerformance('')
      resumeStore.setCompanyId('')
      loginStore.setindustryObj({})
      // 清理岗位数据
      loginStore.setpositionData({})
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setDepartment('')
          resumeStore.setCompany('')
          resumeStore.setWorkDescription('')
          resumeStore.setCompanyId('')
          resumeStore.setSkills('')
          resumeStore.setWorkPerformance('')
          loginStore.setindustryObj({})
          // 清理岗位数据
          loginStore.setpositionData({})
          uni.navigateBack()
        })
    }
  } else {
    console.log(
      objItem.value.startTime,
      formatTime(fromData.value.startTime),
      'objItem.value.startTime',
    )
    if (
      objItem.value.startTime === formatTime(fromData.value.startTime) &&
      objItem.value.endTime === formatTime(fromData.value.endTime) &&
      objItem.value.department === fromData.value.department &&
      objItem.value.company === fromData.value.company &&
      objItem.value.workDescription === fromData.value.workDescription &&
      objItem.value.workPerformance === fromData.value.workPerformance &&
      objItem.value.industry === fromData.value.industry &&
      objItem.value.positionName === fromData.value.positionName
    ) {
      resumeStore.setDepartment('')
      resumeStore.setCompany('')
      resumeStore.setWorkDescription('')
      resumeStore.setSkills('')
      resumeStore.setWorkPerformance('')
      loginStore.setindustryObj({})
      // 清理岗位数据
      loginStore.setpositionData({})
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setDepartment('')
          resumeStore.setCompany('')
          resumeStore.setWorkDescription('')
          resumeStore.setSkills('')
          resumeStore.setWorkPerformance('')
          loginStore.setindustryObj({})
          // 清理岗位数据
          loginStore.setpositionData({})
          uni.navigateBack()
        })
    }
  }
}
// 公司
const goCompany = () => {
  uni.navigateTo({
    url: '/resumeRelated/corporateName/index?company=' + fromData.value.company,
  })
}
// 技能
const goSkills = () => {
  uni.navigateTo({
    url: '/resumeRelated/workExperience/workSkills?skills=' + fromData.value.skills,
  })
}
// 删除
const delWork = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      resumeWorkExperiencesDel({ id: objItem.value.id }).then((res: any) => {
        if (res.code === 0) {
          resumeStore.setDepartment('')
          resumeStore.setCompany('')
          resumeStore.setWorkDescription('')
          resumeStore.setSkills('')
          resumeStore.setWorkPerformance('')
          loginStore.setindustryObj({})
          // 清理岗位数据
          loginStore.setpositionData({})
          uni.navigateBack()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}
// 完成
const addWork = async () => {
  // 调试：打印当前的时间数据
  console.log('提交前的时间数据:', {
    startTime: fromData.value.startTime,
    endTime: fromData.value.endTime,
    startTimeDisplay: startTimeDisplay.value,
    endTimeDisplay: endTimeDisplay.value,
  })
  if (!fromData.value.company) {
    uni.showToast({
      title: '请输入公司名称',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.industry) {
    uni.showToast({
      title: '请选择行业',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.startTime) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.endTime) {
    uni.showToast({
      title: '请选择结束时间',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.positionName) {
    uni.showToast({
      title: '请选择岗位',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.workDescription) {
    uni.showToast({
      title: '请输入工作内容',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  let res
  if (resumeStore.isAdd === 'add') {
    res = await resumeWorkExperiencesAdd({
      ...fromData.value,
      baseInfoId: id.value,
      companyId: companyId.value,
    })
  } else {
    res = await resumeWorkExperiencesUpdate({ ...fromData.value, companyId: companyId.value })
  }

  if (res.code === 0) {
    resumeStore.setDepartment('')
    resumeStore.setCompany('')
    resumeStore.setWorkDescription('')
    resumeStore.setSkills('')
    resumeStore.setWorkPerformance('')
    loginStore.setindustryObj({})
    // 清理岗位数据
    loginStore.setpositionData({})
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 部门
const goDept = () => {
  uni.navigateTo({
    url: '/resumeRelated/workExperience/dept?department=' + fromData.value.department,
  })
}
// 行业
const goIndustry = () => {
  uni.navigateTo({
    url: '/loginSetting/category/expPosition?source=workExperience',
  })
}
// 业绩
const goWorkPerformance = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/workExperience/workPerformance?workPerformance=' +
      fromData.value.workPerformance,
  })
}

// 工作岗位
const handWorkPosition = () => {
  uni.navigateTo({
    url: '/loginSetting/category/career',
  })
}

// 工作内容
const goWorkDescription = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/workExperience/workContent?workDescription=' + fromData.value.workDescription,
  })
}
// 公司
</script>

<style lang="scss" scoped>
::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}

::v-deep .wd-picker__cell {
  width: 100% !important;
  padding: 0rpx !important;
  background: transparent !important;
}

::v-deep .wd-picker__arrow {
  display: none;
}

::v-deep .custom-label-class {
  view {
    font-size: 28rpx !important;
  }
}

:deep(.wd-cell) {
  padding-left: 0rpx !important;
  background-color: transparent;
}

:deep(.wd-cell__wrapper) {
  padding: 0rpx !important;
}

:deep(.time-c) {
  .wd-icon {
    display: none;
  }
}

:deep(.wd-picker__value) {
  color: #333333 !important;
}

:deep(.wd-datetime-picker__placeholder) {
  color: #888888 !important;
}

.w-100 {
  width: 100%;
}

:deep(.wd-picker__placeholder) {
  color: #888888 !important;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;

  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }

  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
      width: 95%;

      .single-line-ellipsis {
        display: block;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }

    .hide-info-option {
      .hide-info-text {
        user-select: none;
      }
    }
  }
}
</style>
