<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg-img">
    <CustomNavBar title="地区"></CustomNavBar>
    <!-- 搜索区域 -->
    <view class="flex-shrink-0 border-b border-gray-200">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image mode="aspectFill" src="/static/img/<EMAIL>"></image>
            </view>
            <view class="content_search_right">
              <wd-input
                v-model="keyword"
                confirm-type="search"
                no-border
                placeholder="搜索您想要的内容"
                @confirm="confirmSearch"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex flex-1 min-h-0">
      <!-- 左侧省份分类 -->
      <scroll-view class="w-220rpx flex-shrink-0" scroll-y>
        <view v-for="(parent, pIndex) in cityList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧城市及区县内容 -->
      <scroll-view class="flex-1 min-w-0" scroll-y>
        <view v-for="(child, cIndex) in currentChildren" :key="cIndex">
          <view class="page-list-right-p">
            <view
              :class="child.active ? 'myStyle-box' : ''"
              class="page-list-right-title"
              @click="selectCity(cIndex)"
            >
              全{{ child.name }}
            </view>
            <view class="page-tag-list">
              <view
                v-for="(grandchild, gIndex) in child.childList"
                :key="gIndex"
                :class="grandchild.active ? 'myStyle-box' : ''"
                class="tag-select-r"
                @click="selectDistrict(cIndex, gIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import cityData from '@/utils/json/city.json'
import { useLoginStore } from '@/store'

const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const cityList = ref([])
const originalCityList = ref([])
const isSearching = ref(false)
const scrollViewHeight = ref(0)

const currentChildren = computed(() => {
  return cityList.value[activeFIndex.value]?.childList || []
})

const fuzzySearch = (searchKeyword: string) => {
  if (!searchKeyword.trim()) return originalCityList.value

  const keyword = searchKeyword.toLowerCase().trim()
  const results = []

  originalCityList.value.forEach((province) => {
    const provinceMatches = province.name.toLowerCase().includes(keyword)
    const matchedCities = []

    if (province.childList) {
      province.childList.forEach((city) => {
        const cityMatches = city.name.toLowerCase().includes(keyword)
        const matchedDistricts =
          city.childList?.filter((district) => district.name.toLowerCase().includes(keyword)) || []
        if (cityMatches || matchedDistricts.length > 0) {
          matchedCities.push({
            ...city,
            childList: cityMatches ? city.childList : matchedDistricts,
          })
        }
      })
    }
    if (provinceMatches || matchedCities.length > 0) {
      results.push({
        ...province,
        childList: provinceMatches ? province.childList : matchedCities,
        _matchType: provinceMatches ? 'province' : 'city',
      })
    }
  })
  return results.sort((a, b) => {
    if (a._matchType === 'province' && b._matchType !== 'province') return -1
    if (b._matchType === 'province' && a._matchType !== 'province') return 1
    return 0
  })
}

const confirmSearch = () => {
  const searchKeyword = keyword.value.trim()
  if (searchKeyword) {
    isSearching.value = true
    const searchResults = fuzzySearch(searchKeyword)
    cityList.value = searchResults
    if (searchResults.length > 0) {
      activeFIndex.value = 0
    }
  } else {
    clearSearch()
  }
}

const clearSearch = () => {
  isSearching.value = false
  cityList.value = originalCityList.value
  keyword.value = ''

  const selectedCity = loginStore.cityObj
  if (selectedCity) {
    findAndSetSelectedCity(selectedCity)
  }
}

watch(keyword, (newKeyword) => {
  if (!newKeyword.trim() && isSearching.value) {
    clearSearch()
  }
})

const activeF = (index) => {
  activeFIndex.value = index
}
const resetAllActive = () => {
  for (const parent of cityList.value) {
    for (const child of parent.childList || []) {
      child.active = false
      for (const grandchild of child.childList || []) {
        grandchild.active = false
      }
    }
  }
}
const buildCitySelection = (provinceIndex, cityIndex, districtIndex = null) => {
  const province = cityList.value[provinceIndex]
  const city = province.childList[cityIndex]

  const citySelection = {
    provinceName: province.name,
    provinceCode: province.code,
    cityCode: city.code,
    cityName: city.name,
    districtCode: '',
    districtName: '',
  }

  if (districtIndex !== null && city.childList?.[districtIndex]) {
    const district = city.childList[districtIndex]
    citySelection.districtCode = district.code
    citySelection.districtName = district.name
  }

  return citySelection
}

const completeSelection = (citySelection) => {
  loginStore.setCity(citySelection)
  uni.navigateBack()
}
const selectCity = (cityIndex) => {
  resetAllActive()

  const targetCity = cityList.value[activeFIndex.value].childList[cityIndex]
  targetCity.active = true

  const citySelection = buildCitySelection(activeFIndex.value, cityIndex)
  completeSelection(citySelection)
}
const selectDistrict = (cityIndex, districtIndex) => {
  resetAllActive()
  const targetDistrict =
    cityList.value[activeFIndex.value].childList[cityIndex].childList[districtIndex]
  targetDistrict.active = true
  const citySelection = buildCitySelection(activeFIndex.value, cityIndex, districtIndex)
  completeSelection(citySelection)
}
const initializeCityData = () => {
  const list = CommonUtil.deepClone(cityData.zpData.cityList)
  console.log(list, 'list===')
  return list
}
const findAndSetSelectedCity = (targetCity) => {
  if (!targetCity?.provinceCode) return false
  const provinceIndex = cityList.value.findIndex(
    (province) => province.code === targetCity.provinceCode,
  )
  if (provinceIndex === -1) return false
  activeFIndex.value = provinceIndex
  const province = cityList.value[provinceIndex]
  if (!province.childList) return false

  const cityIndex = province.childList.findIndex((city) => city.code === targetCity.cityCode)
  if (cityIndex === -1) return false
  const city = province.childList[cityIndex]

  if (targetCity.districtCode && city.childList) {
    const districtIndex = city.childList.findIndex(
      (district) => district.code === targetCity.districtCode,
    )
    if (districtIndex !== -1) {
      resetAllActive()
      city.childList[districtIndex].active = true
      return true
    }
  }
  resetAllActive()
  city.active = true
  return true
}

onLoad(() => {
  const initializedData = initializeCityData()

  originalCityList.value = initializedData
  cityList.value = initializedData
  const selectedCity = loginStore.cityObj
  if (selectedCity) {
    findAndSetSelectedCity(selectedCity)
  }
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  width: 100%;
  background-color: transparent !important;

  .wd-input__placeholder {
    font-size: 28rpx !important;
    color: #333333 !important;
  }

  .wd-input__inner {
    font-size: 28rpx !important;
    font-weight: 500;
    color: #333333 !important;
  }
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0 !important;
}

.myStyle-box {
  position: relative;
  border: 1px solid #1160ff;
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('@/loginSetting/img/Mask_group(2).png');
  background-repeat: no-repeat; /* 防止平铺 */
  background-position: center; /* 保证居中显示 */
  background-size: cover; /* 让背景图完全覆盖容器 */
  border-radius: 10rpx 0 10rpx 0;
}

.page-list-right-title {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #555;
  text-align: center;
  background-color: #fff !important;
  border-radius: 10rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #f5f4f4;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 25rpx 40rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #333333;
      background: #ffffff;
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 220rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #333;
    text-align: center;
    background: #ffffff;
    border-radius: 10rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
