<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    :fixed="false"
    :paging-style="pageStyle"
    :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    empty-view-text="暂无消息通知"
    safe-area-inset-bottom
    @query="queryList"
  >
    <template #top>
      <CustomNavBar :title="handlerTitle()"></CustomNavBar>
    </template>

    <view class="message-list-container">
      <view
        v-for="(infoObj, index) in pageData"
        :key="`message-${infoObj.id || index}`"
        class="message-item"
        @click="handleMessageClick(infoObj)"
      >
        <view class="message-header">
          <view class="message-title">
            {{ infoObj.title }}
          </view>
          <view class="message-time">
            {{ formatDate(infoObj.createAdminTime) }}
          </view>
        </view>
        <view class="message-content">
          {{ infoObj.content }}
        </view>
        <!-- <view class="message-footer">
          <view class="message-status" :class="getStatusClass(infoObj)">
            {{ getStatusText(infoObj) }}
          </view>
        </view> -->
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryDetailList } from '@/interPost/messege'
import { getCustomBar } from '@/utils/storage'

const customBar = ref(null)
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
    padding: '20rpx 0rpx 0rpx',
  },
})

const messageTypeCode = ref('')

const params = ref({
  entity: {
    messageType: null,
  },
  orderBy: {},
})

const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}年${month}月${day}日`
}

const handlerTitle = () => {
  const titleMap = {
    '0': '系统通知',
    '1': '道具通知',
    '2': '认证通知',
    '3': '违规通知',
  }
  return titleMap[messageTypeCode.value] || '消息通知'
}

const getStatusClass = (item: any) => {
  if (messageTypeCode.value === '3') {
    return 'status-warning'
  }
  return 'status-normal'
}

const getStatusText = (item: any) => {
  const statusMap = {
    '0': '系统消息',
    '1': '道具消息',
    '2': '认证消息',
    '3': '违规提醒',
  }
  return statusMap[messageTypeCode.value] || '消息'
}

const handleMessageClick = (item: any) => {
  console.log('点击消息:', item)
}

const queryList = async (page: number, size: number) => {
  try {
    pageSetInfo(page, size)
    params.value.entity.messageType = Number(messageTypeCode.value)

    const res: any = await queryDetailList({
      ...params.value,
      page: pageInfo.page,
      size: pageInfo.size,
    })

    if (res.code === 0 && res.data?.list) {
      const processedData = res.data.list.map((item: any, index: number) => ({
        ...item,
        id: item.id || `temp-${Date.now()}-${index}`,
        isRead: item.isRead || false,
        priority: item.priority || 'normal',
      }))

      pagingRef.value.complete(processedData)
    } else {
      pagingRef.value.complete(false)
      uni.showToast({
        title: res.message || '获取消息列表失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取消息列表失败:', error)
    pagingRef.value.complete(false)
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none',
    })
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  messageTypeCode.value = options.messageType || '0'
  params.value.entity.messageType = options.messageType || '0'
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.message-list-container {
  padding: 20rpx 30rpx;
}

.message-item {
  padding: 30rpx;
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-height: 120rpx;

  &:active {
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
    transform: scale(0.98);
  }
}

.message-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.message-title {
  flex: 1;
  margin-right: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.4;
  color: #333;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.message-content {
  margin-bottom: 20rpx;
  min-height: 80rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  word-wrap: break-word;
  word-break: break-all;
}

.message-footer {
  display: flex;
  justify-content: flex-end;
}

.message-status {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border-radius: 20rpx;

  &.status-normal {
    color: #3b82f6;
    background: #f0f9ff;
  }

  &.status-warning {
    color: #d97706;
    background: #fef3c7;
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;

  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
