<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar>
        <template #right v-if="!userRoleIsBusiness">
          <wd-img
            v-if="jobDetails.collectStatus === 0"
            :height="22"
            :src="collect"
            :width="22"
            @click="collectFun"
          />
          <wd-img
            v-if="jobDetails.collectStatus === 1"
            :height="22"
            :src="noCollect"
            :width="22"
            @click="nocollectFun"
          />
        </template>
      </CustomNavBar>
    </template>

    <view class="jobDetail-list">
      <view class="jobDetail-title flex-between">
        <view class="jobDetail-title-main">
          {{ jobDetails.positionMarkName ? jobDetails.positionMarkName : jobDetails.positionName }}
        </view>
        <view class="jobDetail-title-salary">
          {{
            formatSalary(
              jobDetails.workSalaryBegin,
              jobDetails.workSalaryEnd,
              jobDetails.salaryMonths,
              '月',
            )
          }}
        </view>
      </view>
      <view class="jobDetail-subtitle">
        <view class="flex-c">
          <wd-icon color="#888" name="location" size="16px"></wd-icon>
          <view class="m-l-2rpx subText">
            {{
              handleAddress(jobDetails.provinceName, jobDetails.cityName, jobDetails.districtName)
            }}
          </view>
        </view>
        <view v-if="jobDetails.isToDayPublish" class="subText jobDetail-subtitle-text">
          该岗位于今日新发布
        </view>
      </view>
      <view class="jobDetail-card flex-c" @click="goChat">
        <view
          :class="
            jobDetails.hxUserInfoVO?.isOnline || jobDetails.activityStatus === 1
              ? 'border-twinkle bg_left_icon_box'
              : 'mt-10rpx'
          "
        >
          <image
            v-if="jobDetails.sex === 1"
            :src="jobDetails.hrUrl ? jobDetails.hrUrl : '/static/header/hrheader1.png'"
            class="jobDetail-card-img"
            mode="aspectFill"
          ></image>
          <image
            v-else
            :src="jobDetails.hrUrl ? jobDetails.hrUrl : '/static/header/hrheader2.png'"
            class="jobDetail-card-img"
            mode="aspectFill"
          ></image>
        </view>
        <view class="jobDetail-card-bg">
          <view class="jobDetail-card-info">
            <view class="jobDetail-card-content">
              <view class="jobDetail-card-name text-28rpx p-b-6rpx flex items-center">
                {{ jobDetails.hrPositionName }}
                <text v-if="jobDetails.hrPosition">·</text>
                <text class="c-#666 text-24rpx">
                  {{ truncateText(jobDetails.hrPosition, 10) }}
                </text>
                <view
                  v-if="jobDetails.hxUserInfoVO?.isOnline || jobDetails.activityStatus === 1"
                  class="m-l-10rpx border-#34A715 border-1rpx border-solid c-#34A715 text-22rpx inline-block rounded-8rpx p-r-10rpx p-l-10rpx"
                >
                  在线
                </view>
              </view>
              <view class="subText">{{ truncateText(jobDetails.company, 14) }}</view>
              <!-- <view class="subText">今日回复6次</view> -->
            </view>
            <view v-if="!userRoleIsBusiness" class="jobDetail-chat-icon">
              <image
                class="chat-icon"
                mode="aspectFill"
                src="/static/images/home/<USER>"
              ></image>
              <view
                v-if="jobDetails.hxUserInfoVO?.isOnline || jobDetails.activityStatus === 1"
                class="chat-dots-animation"
              >
                <view class="dot dot1"></view>
                <view class="dot dot2"></view>
                <view class="dot dot3"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="jobDetail-content">
        <view class="jobDetail-content-title p-t-20rpx">岗位详情</view>
        <view v-if="jobDetails.positionKey" class="jobDetail-tag-list flex-c m-b-20rpx">
          <view v-for="(item, index) in jobDetails.positionKey" :key="index" class="jobDetail-tag">
            {{ item }}
          </view>
        </view>
        <!-- <view class="jobDetail-content-name">岗位要求</view> -->
        <div class="jobDetail-content-name text-pre-wrap">{{ jobDetails.positionDesc }}</div>
      </view>
      <view v-if="jobDetails.positionBenefitVOList?.length" class="jobDetail-content">
        <view class="jobDetail-content-title p-t-40rpx">福利待遇</view>
        <view class="jobDetail-tag-list flex-c">
          <view
            v-for="(item, index) in jobDetails.positionBenefitVOList"
            :key="index"
            class="jobDetail-tag"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <!-- 工作地址 -->
      <view class="jobDetail-content-title p-t-30rpx">工作地址</view>
      <view class="jobDetail-address-card" @click="goMap">
        <view class="jobDetail-address-content">
          <view class="jobDetail-address-info">
            <view class="jobDetail-address-text">{{ jobDetails.regAddress }}</view>
          </view>
          <view v-if="jobDetails.distanceMeters" class="jobDetail-distance-info">
            <wd-icon color="#888" name="location" size="14px"></wd-icon>
            <view class="jobDetail-distance-text">
              {{ `距离我${jobDetails.distanceMeters}` }}
            </view>
          </view>
        </view>
        <view class="jobDetail-address-icon">
          <image
            class="address-icon"
            mode="aspectFill"
            src="/static/images/home/<USER>"
          ></image>
        </view>
      </view>
      <!-- 公司信息 -->
      <view class="jobDetail-content-title p-t-40rpx">公司信息</view>
      <view class="jobDetail-conpany flex-between" @click="goCompany">
        <view class="flex-c">
          <!-- <image class="jobDetail-conpany-img" src="/static/img/1.jpg"></image> -->
          <image
            :src="jobDetails.companyLogoUrl ? jobDetails.companyLogoUrl : '/static/header/logo.png'"
            class="jobDetail-conpany-img"
            mode="aspectFill"
          ></image>
          <view class="jobDetail-conpany-text">
            <view class="jobDetail-conpany-title">
              {{ truncateText(jobDetails.company, 12) }}
            </view>
            <view class="flex-c">
              <view class="jobDetail-conpany-subtitle">
                {{ truncateText(jobDetails.industryName, 8) }}
              </view>
              <view class="jobDetail-conpany-subtitle p-l-40rpx">{{ jobDetails.sizeName }}</view>
            </view>
          </view>
        </view>
        <view v-if="!userRoleIsBusiness" class="jobDetail-card-icon">
          <wd-icon class="arrow-right-1" color="#317CFF" name="chevron-right" size="15px"></wd-icon>
        </view>
      </view>
      <view
        class="notice-box bg-#fff p-36rpx m-t-58rpx m-b-40rpx border-rd-40rpx flex items-center"
      >
        <wd-img :height="30" :src="notice" :width="30" class="m-r-20rpx"></wd-img>
        <view class="text-22rpx c-#ff0000 flex-1 text-left">
          以任何形式向求职者收取财(物)的行为均违反《中华人民共和国劳动法》，请注意防范，避免损失！
        </view>
      </view>
    </view>

    <template #bottom>
      <view v-if="!userRoleIsBusiness" class="btn_fixed">
        <view class="btn_container">
          <view class="btn_box" @click.stop="goJob">
            <view class="btn_bl">投递简历</view>
          </view>
          <view class="btn_box" @click.stop="goChat">
            <view class="btn_bg">立即沟通</view>
          </view>
        </view>
      </view>
      <view v-else class="btn_fixed">
        <view class="btn_container" v-if="jobStatus === 1">
          <view class="btn_box" @click="handleEditPosition">
            <view class="btn_bl">修改岗位</view>
          </view>
          <view v-if="jobStatus === 1" class="btn_box" @click="handleClosePosition">
            <view class="btn_bg">关闭岗位</view>
          </view>
        </view>
        <view v-else-if="jobStatus === 2" class="btn_box" @click="handlePublishPosition">
          <view class="btn_bg_rest">重新发布</view>
        </view>
      </view>
    </template>
  </z-paging>

  <!-- 自定义弹窗 -->
  <view v-if="showCustomModal" class="custom-modal-overlay" @click="closeCustomModal">
    <view class="custom-modal-container" @click.stop>
      <view class="custom-modal-content">
        <view class="custom-modal-header">
          <text class="custom-modal-title">提示</text>
        </view>
        <view class="custom-modal-body">
          <view class="custom-modal-message">
            <text>请上传</text>
            <text class="red-text">附件简历</text>
            <text>后再进行投递</text>
          </view>
        </view>
        <view class="custom-modal-footer">
          <view class="custom-modal-btn custom-modal-btn-cancel" @click="closeCustomModal">
            取消
          </view>
          <view class="custom-modal-btn custom-modal-btn-confirm" @click="confirmCustomModal">
            确定
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { handleNavigation } from '@/utils/open-map-url'
import { userLoginExecute } from '@/utils/common/userPermission'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import {
  positionInfoDetail,
  cancelPosition,
  collectPosition,
  closePosition,
} from '@/interPost/home'
import { positionInfoQueryDetailByTourist } from '@/service/positionInfo'
import { truncateText, formatSalary } from '@/utils/util'
import collect from '@/static/img/positionSc.png'
import noCollect from '@/static/img/noCollect.png'
import notice from '@/resumeRelated/img/notice.png'

const message = useMessage()
const { getDictLabel } = useDictionary()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { userRoleIsRealName, userRoleIsBusiness } = useUserInfo()
const { pageParams } = usePagePeriod()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const jobDetails = ref<AnyObject>({})
const imgMap = ref('')
const jobStatus = ref(null)
const showCustomModal = ref(false)
const sourceIsTourist = computed(() => pageParams.value?.source === 'tourist')

const getDetail = async () => {
  if (sourceIsTourist.value) {
    const { data } = await positionInfoQueryDetailByTourist({
      id: pageParams.value?.id,
    })
    jobDetails.value = data
  } else {
    const { data } = await positionInfoDetail({ id: pageParams.value?.id })
    jobDetails.value = data
  }
  jobDetails.value.positionKey = jobDetails.value.positionKey
    ? jobDetails.value.positionKey.split(',')
    : ''
  jobDetails.value.sizeName = await getDictLabel(100, jobDetails.value.sizeName)
  jobDetails.value.workEducational = await getDictLabel(10, jobDetails.value.workEducational)
  if (Array.isArray(jobDetails.value.positionKey)) {
    if (
      typeof jobDetails.value.workExperienceStart === 'number' &&
      typeof jobDetails.value.workExperienceEnd === 'number'
    ) {
      if (jobDetails.value.workExperienceStart === 0 && jobDetails.value.workExperienceEnd === 0) {
        jobDetails.value.positionKey.unshift('经验不限')
      } else {
        jobDetails.value.positionKey.unshift(
          `${jobDetails.value.workExperienceStart}-${jobDetails.value.workExperienceEnd}年`,
        )
      }
    }
    if (typeof jobDetails.value.workEducational === 'string') {
      if (jobDetails.value.workEducational === '全部') {
        jobDetails.value.positionKey.unshift('学历不限')
      } else {
        jobDetails.value.positionKey = [
          jobDetails.value.workEducational,
          ...jobDetails.value.positionKey,
        ]
      }
    }
  }
  jobStatus.value = jobDetails.value.status
  try {
    const eleData = uni.$UIKit.appUserStore.getUserInfoFromStore(
      jobDetails.value.hxUserInfoVO.username,
    )
    jobDetails.value.hxUserInfoVO.isOnline = !!eleData?.isOnline
  } catch (error) {}
  if (jobDetails.value.distanceMeters) {
    const distance = Math.floor(parseInt(jobDetails.value.distanceMeters) / 1000)
    if (distance === 0) {
      jobDetails.value.distanceMeters = '<1km'
    } else {
      jobDetails.value.distanceMeters = distance + 'km'
    }
  }
}

const handleAddress = (provinceName: any, cityName: any, districtName: any) => {
  const province = provinceName || ''
  let city = cityName || ''
  if (province && city && province === city) {
    city = ''
  }
  const parts = [province, city].filter(Boolean)
  if (districtName) {
    parts.push(districtName)
  }
  return parts.join('·')
}

const collectFun = async () => {
  await collectPosition({ id: jobDetails.value.id })
  jobDetails.value.collectStatus = 1
}
const nocollectFun = async () => {
  await cancelPosition({ id: jobDetails.value.id })
  jobDetails.value.collectStatus = 0
}
const goMap = () => {
  handleNavigation({
    latitude: jobDetails.value.lat,
    longitude: jobDetails.value.lon,
    name: jobDetails.value.company,
  })
}
const getMapKet = () => {
  const lat = jobDetails.value.lat
  const lon = jobDetails.value.lon
  const staticKey = jobDetails.value.staticKey
  imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${lon},${lat}&width=300&height=200&zoom=12&tk=${staticKey}&markers=${lon},${lat}`
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
const goJob = userLoginExecute(async () => {
  try {
    await userToRealName()
    const hxUserInfoVO = jobDetails.value?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, jobDetails.value)
      if (!num) {
        showCustomModal.value = true
      }
    }
  } catch (error) {}
})
const goChat = userLoginExecute(async () => {
  try {
    await userToRealName()
    const hxUserInfoVO = jobDetails.value?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, jobDetails.value)
    }
  } catch (error) {}
})

const handleEditPosition = async () => {
  uni.navigateTo({
    url: `/sub_business/pages/release/index?positionId=${Number(jobDetails.value.id)}&source=positionEdit`,
  })
}

const handleClosePosition = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要关闭岗位吗?',
    })
    .then(async () => {
      await closePosition({ id: jobDetails.value.id })
      uni.showToast({
        title: '岗位已关闭',
        icon: 'none',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    })
}

const handlePublishPosition = async () => {
  uni.navigateTo({
    url: `/sub_business/pages/release/index?positionId=${jobDetails.value.id}&source=positionEdit`,
  })
}
const goCompany = () => {
  if (!userRoleIsBusiness.value) {
    uni.navigateTo({
      url: `/resumeRelated/company/index?companyId=${jobDetails.value.companyId}&id=${jobDetails.value.id}`,
    })
  }
}

// 关闭自定义弹窗
const closeCustomModal = () => {
  showCustomModal.value = false
}

// 确认自定义弹窗
const confirmCustomModal = () => {
  showCustomModal.value = false
  uni.navigateTo({
    url: '/resumeRelated/AttachmentResume/index',
  })
}
onMounted(async () => {
  await uni.$onLaunched
  await getDetail()
  getMapKet()
})
</script>

<style lang="scss" scoped>
.btn_fixed {
  .btn_container {
    display: flex;
    gap: 20rpx;
    padding: 0rpx 40rpx 20rpx;
    margin-top: 30rpx;
  }

  .btn_box {
    box-sizing: border-box;
    flex: 1;

    .btn_bl {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(271deg, #95a5f7 0%, #b0fffc 99%);
      border-radius: 14px 14px 14px 14px;
    }

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
    .btn_bg_rest {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      margin: 40rpx 60rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
    .btn_bg_business {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(271deg, #95a5f7 0%, #b0fffc 99%);
      border-radius: 14px 14px 14px 14px;
    }

    .btn_bg_close {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
      background: #ff6b6b;
      border-radius: 14px 14px 14px 14px;
    }

    .btn_bg_publish {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
      background: #527bff;
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.jobDetail-list {
  padding: 40rpx;
  border-radius: 36rpx;

  .jobDetail-title {
    .jobDetail-title-main {
      font-size: 36rpx;
      font-weight: bold;
      line-height: 44rpx;
      color: #000;
    }

    .jobDetail-title-salary {
      font-size: 28rpx;
      color: #ff5050;
    }
  }

  .jobDetail-subtitle {
    padding-top: 20rpx;

    .jobDetail-subtitle-text {
      line-height: 44rpx;
    }
  }

  .jobDetail-card {
    padding: 40rpx 0rpx 20rpx;

    .jobDetail-card-img {
      width: 104rpx;
      height: 104rpx;
      border-radius: 30rpx;
      box-shadow: 0px 20rpx 42rpx 0px rgba(0, 0, 0, 0.3);
    }

    .border-twinkle {
      position: relative;

      &::before {
        position: absolute;
        top: -2rpx;
        right: -2rpx;
        bottom: -2rpx;
        left: -2rpx;
        z-index: -1;
        content: '';
        background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
        border-radius: 30rpx;
        animation: twinkle 2s infinite;
      }
    }

    .bg_left_icon_box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 104rpx;
      height: 108rpx;
      border: 2rpx solid #0ea500;
      border-radius: 30rpx;
    }

    @keyframes twinkle {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    @keyframes dotPulse {
      0%,
      80%,
      100% {
        opacity: 0.3;
        transform: scale(0.8);
      }
      40% {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    .jobDetail-card-bg {
      z-index: -3;
      display: flex;
      flex-direction: column;
      align-items: left;
      justify-content: center;
      width: 100%;
      height: 140rpx;
      padding: 20rpx 20rpx 20rpx 70rpx;
      margin: 2rpx 0 0 -50rpx;
      background-color: #ffffff;
      border-radius: 36rpx;
      box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);
    }

    .jobDetail-card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .jobDetail-card-content {
        flex: 1;
      }

      .jobDetail-chat-icon {
        position: relative;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 112rpx;
        height: 74rpx;
        text-align: center;
        background: #fff4f4;
        border-radius: 20rpx 20rpx 20rpx 20rpx;

        .chat-icon {
          width: 48rpx;
          height: 48rpx;
          opacity: 0.7;

          &_icon {
            width: 40rpx;
            height: 40rpx;
          }

          &_icon-1 {
            width: 40rpx;
            height: 40rpx;
          }
        }

        .chat-dots-animation {
          position: absolute;
          bottom: 8rpx;
          left: 50%;
          display: flex;
          gap: 3rpx;
          align-items: center;
          transform: translateX(-50%);

          .dot {
            width: 8rpx;
            height: 8rpx;
            background-color: #ff6b6b;
            border-radius: 50%;
            animation: dotPulse 1.5s infinite ease-in-out;
          }

          .dot1 {
            animation-delay: 0s;
          }

          .dot2 {
            animation-delay: 0.3s;
          }

          .dot3 {
            animation-delay: 0.6s;
          }
        }
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      margin-left: -40rpx;
      background-color: #aeffb1;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-tag-list {
    flex-wrap: wrap;
    padding: 30rpx 0rpx 0rpx;

    .jobDetail-tag {
      // display: flex;
      // flex-wrap: wrap;
      // align-items: center;
      padding: 5rpx 30rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      font-size: 26rpx;
      color: #333333;
      text-align: center;
      background-color: #ebebeb;
      border-radius: 16rpx;
    }
  }

  .jobDetail-content {
    .jobDetail-content-name {
      font-size: 28rpx;
      line-height: 50rpx;
      color: #333333;
    }
  }

  .jobDetail-content-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #000000;
  }

  .jobDetail-conpany {
    padding: 20rpx;
    margin: 40rpx 0;
    background: #ffffff;
    border-radius: 36rpx;
    box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);

    .jobDetail-conpany-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 34rpx;
    }

    .jobDetail-conpany-text {
      margin-left: 20rpx;

      .jobDetail-conpany-title {
        padding-bottom: 10rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .jobDetail-conpany-subtitle {
        font-size: 24rpx;
        color: #000000;
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      background-color: #bce9ff;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-address-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 40rpx;
    margin: 30rpx 0 0 0;
    background: #ffffff;
    border-radius: 36rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .jobDetail-address-content {
      flex: 1;

      .jobDetail-company-title {
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: 600;
        line-height: 1.2;
        color: #000000;
      }

      .jobDetail-address-info {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .jobDetail-address-text {
          margin-left: 8rpx;
          font-size: 28rpx;
          font-weight: 500;
          line-height: 1.3;
          color: #333333;
        }
      }

      .jobDetail-distance-info {
        display: flex;
        align-items: center;

        .jobDetail-distance-text {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #888888;
        }
      }
    }

    .jobDetail-address-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      margin-left: 20rpx;
      background-color: #f5f5f5;
      border-radius: 20rpx;

      .address-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .notice-box {
    box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);
  }
}

// 自定义弹窗样式
.custom-modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.custom-modal-container {
  width: 600rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 20rpx;
}

.custom-modal-content {
  padding: 40rpx;
}

.custom-modal-header {
  margin-bottom: 30rpx;
  text-align: center;

  .custom-modal-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.custom-modal-body {
  margin-bottom: 40rpx;
  text-align: center;

  .custom-modal-message {
    font-size: 26rpx;
    font-weight: normal;
    line-height: 1.5;
    color: #555555;

    .red-text {
      font-weight: normal;
      color: #ff0000;
    }
  }
}

.custom-modal-footer {
  display: flex;
  gap: 28rpx;

  .custom-modal-btn {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 70rpx;
    font-size: 26rpx;
    font-weight: normal;
    cursor: pointer;
    border-radius: 40rpx;

    &.custom-modal-btn-cancel {
      font-weight: normal;
      color: #000000;
      background-color: #f5f5f5;
    }

    &.custom-modal-btn-confirm {
      font-weight: normal;
      color: #ffffff;
      background-color: #0096ff;
    }
  }
}
</style>
