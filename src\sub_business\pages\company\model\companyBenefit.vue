<route lang="json5">
{ layout: 'default', style: { navigationBarTitleText: '', navigationStyle: 'custom' } }
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="福利待遇">
        <template #left>
          <wd-icon class="back-button" color="#000" name="arrow-left" size="20" @click="back" />
        </template>
      </CustomNavBar>
    </template>
    <view class="mt-60rpx px-66rpx flex flex-col gap-106rpx">
      <view class="flex flex-col gap-38rpx">
        <!--        <view class="flex items-center gap-26rpx">-->
        <!--          <wd-input-->
        <!--            type="text"-->
        <!--            placeholder="自定义标签"-->
        <!--            v-model="labelCustomVal"-->
        <!--            no-border-->
        <!--            @confirm="handleAddCustomLabel"-->
        <!--          />-->
        <!--          <view-->
        <!--            class="center size-90rpx bg-[#5378FF] shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx"-->
        <!--            @click="handleAddCustomLabel"-->
        <!--          >-->
        <!--            <wd-icon name="add" size="26rpx" color="#ffffff" />-->
        <!--          </view>-->
        <!--        </view>-->
        <view v-if="labelActiveList.length" class="flex items-center flex-wrap gap-16rpx">
          <view
            v-for="(item, key) in labelActiveList"
            :key="`label-active-${key}`"
            class="center gap-10rpx h-56rpx px-6rpx rounded-10rpx border-1px border-dashed border-[#5378FF]"
          >
            <image :src="getBenefitsIcon(item.welfareTreatment)" class="w-32rpx h-32rpx" />
            <text class="c-#5378FF text-24rpx font-400">{{ item.welfareTreatment }}</text>
            <wd-icon
              color="#5378FF"
              name="close-circle"
              size="24rpx"
              @click="handleDeleteLabel(key)"
            />
          </view>
        </view>
      </view>
      <view v-if="labelList.length" class="flex flex-col gap-38rpx">
        <text class="c-#333333 font-bold text-36rpx">可选标签</text>
        <view class="flex items-center flex-wrap gap-30rpx">
          <view
            v-for="(item, key) in labelList.filter(
              (label) =>
                !labelActiveList.some(
                  (active) => active.welfareTreatment === label.welfareTreatment,
                ),
            )"
            :key="`label-${key}`"
            class="center gap-10rpx h-56rpx px-10rpx rounded-10rpx border-1px border-dashed border-[#888888]"
            @click="handleSelectLabel(item)"
          >
            <image :src="getBenefitsIcon(item.welfareTreatment)" class="w-32rpx h-32rpx" />
            <text class="c-#888888 text-24rpx font-400">{{ item.welfareTreatment }}</text>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="handleConfirm">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { updateWelfare } from '@/service/hrCompany'
import { useBenefitsIcon } from '@/sub_business/hooks/useBenefitsIcon'

// 使用福利图标hooks
const { getBenefitsIcon } = useBenefitsIcon()

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const salaryArray = [
  { welfareTreatment: '五险一金' },
  { welfareTreatment: '年终奖' },
  { welfareTreatment: '定期团建' },
  { welfareTreatment: '零食下午茶' },
  { welfareTreatment: '节日福利' },
  { welfareTreatment: '生日福利' },
  { welfareTreatment: '带薪年假' },
  { welfareTreatment: '员工食堂' },
  { welfareTreatment: '定期体检' },
  { welfareTreatment: '交通补贴' },
  { welfareTreatment: '通讯补贴' },
  { welfareTreatment: '餐饮补贴' },
  { welfareTreatment: '住房补贴' },
  { welfareTreatment: '免费工作餐' },
  { welfareTreatment: '提供住宿' },
  { welfareTreatment: '免费班车' },
  { welfareTreatment: '岗位晋升' },
  { welfareTreatment: '年度调薪' },
  { welfareTreatment: '高温补贴' },
  { welfareTreatment: '弹性工作' },
]
// 返回函数
const back = () => {
  uni.navigateBack()
}

// 定义福利待遇类型
interface WelfareItem {
  welfareTreatment: string
}

const labelCustomVal = ref('')
const labelActiveList = ref<WelfareItem[]>([])
const labelList = ref<WelfareItem[]>([])

function handleSelectLabel(label: WelfareItem) {
  if (!labelActiveList.value.includes(label)) {
    labelActiveList.value.push(label)
  }
}

function handleDeleteLabel(key: number) {
  labelActiveList.value.splice(key, 1)
}

function handleAddCustomLabel() {
  if (labelCustomVal.value) {
    const exists = labelActiveList.value.some(
      (item) => item.welfareTreatment === labelCustomVal.value,
    )
    if (!exists) {
      labelActiveList.value.push({ welfareTreatment: labelCustomVal.value })
      labelCustomVal.value = ''
    }
  }
}

// 初始化标签列表
const updateLabelList = () => {
  // 设置可选标签为福利待遇标签
  labelList.value = salaryArray
}

// 确认选择
const handleConfirm = async () => {
  try {
    // 调用更新公司福利待遇接口
    const res: any = await updateWelfare({
      welfareList: labelActiveList.value.map((item) => item.welfareTreatment),
    })

    if (res.code === 0) {
      uni.navigateBack({
        delta: 1,
        success() {
          uni.$emit('welfare-updated')
        },
      })
    } else {
      uni.showToast({
        title: res.msg || '更新失败',
        icon: 'none',
        duration: 3000,
      })
    }
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'none',
      duration: 3000,
    })
  }
}

// 页面加载时处理参数和初始化
onLoad((options) => {
  const companyWelfareList = JSON.parse(decodeURIComponent(options.companyWelfareList)) || []
  // 初始化可选标签
  updateLabelList()

  // 处理传递过来的福利待遇参数
  if (options.companyWelfareList) {
    try {
      const companyWelfareList = JSON.parse(decodeURIComponent(options.companyWelfareList))
      if (Array.isArray(companyWelfareList)) {
        // 将字符串数组转换为对象数组
        labelActiveList.value = companyWelfareList.map((item) => {
          if (typeof item === 'string') {
            return { welfareTreatment: item }
          } else if (item && typeof item === 'object' && item.welfareTreatment) {
            return item
          } else {
            return { welfareTreatment: String(item) }
          }
        })
      }
    } catch (error) {
      console.error('解析福利待遇参数失败:', error)
      labelActiveList.value = []
    }
  }
})

defineExpose({
  submitData: handleConfirm,
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  flex: 1;
  padding: 22rpx 22rpx;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
}

.btn-fixed {
  padding: 40rpx 80rpx;

  .btn_box {
    box-sizing: border-box;
    width: 100%;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 25rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
