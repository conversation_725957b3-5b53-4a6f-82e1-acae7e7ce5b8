<template>
  <view :class="['coupon-card', cardClass]">
    <view class="coupon-main">
      <view class="coupon-left">
        <view :class="['coupon-amount', amountClass]">
          <text class="currency">{{ coupon.type === 2 ? '折' : '￥' }}</text>
          <text class="amount">{{ couponAmount }}</text>
        </view>
      </view>
      <wd-divider
        :color="dividerColor"
        class="coupon-divider-vertical"
        dashed
        vertical
      ></wd-divider>

      <view class="coupon-right">
        <view :class="['coupon-info', infoClass]">
          <view class="coupon-title">{{ coupon.propName }}</view>
          <view class="coupon-desc">{{ couponDesc }}</view>
          <view v-if="status === 'used'" class="coupon-used-jobs">
            岗位：{{ coupon.jobs || '电商管理' }}
          </view>
        </view>
        <view v-if="status === 'unused'" class="coupon-action">
          <wd-button custom-class="use-btn" size="small" @click="handleUseCoupon">
            立即使用
          </wd-button>
        </view>
      </view>
    </view>
    <view class="coupon-divider-horizontal">
      <wd-divider :color="dividerColor" dashed></wd-divider>
    </view>
    <view :class="['coupon-validity', validityClass]">
      {{ validityText }}
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  coupon: any
  status: 'unused' | 'used' | 'expired'
}

interface Emits {
  (e: 'use-coupon', coupon: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const couponAmount = computed(() => {
  if (props.coupon.type === 2) {
    return props.coupon.discountValue
  }
  return props.coupon.minOrderAmount / 100
})

const couponDesc = computed(() => {
  if (props.coupon.type === 2) {
    return '无门槛折扣卷'
  } else if (props.coupon.type === 1) {
    return `满500元减${props.coupon.minOrderAmount / 100}元`
  } else {
    return '无门槛抵扣券'
  }
})

const validityText = computed(() => {
  switch (props.status) {
    case 'unused':
      return `有效期至${props.coupon.expiredTime}`
    case 'used':
      return `使用时间${props.coupon.receivedTime}`
    case 'expired':
      return `有效期至${props.coupon.expiredTime}`
    default:
      return ''
  }
})

// 样式类名
const cardClass = computed(() => {
  return props.status === 'unused' ? '' : `${props.status}-card`
})

const amountClass = computed(() => {
  return props.status === 'unused' ? '' : `${props.status}-amount`
})

const infoClass = computed(() => {
  return props.status === 'unused' ? '' : `${props.status}-info`
})

const validityClass = computed(() => {
  return props.status === 'unused' ? '' : `coupon-validity-${props.status}`
})

const dividerColor = computed(() => {
  return props.status === 'unused' ? '#ff2b75' : '#636363'
})

const handleUseCoupon = () => {
  emit('use-coupon', props.coupon)
}
</script>

<style lang="scss" scoped>
// 优惠券卡片样式
.coupon-card {
  position: relative;
  margin-bottom: 30rpx;
  overflow: visible;
  background: url('@/static/my/business/coupons_normal.png') no-repeat center center;
  background-size: cover;
  border: none;
  border-radius: 24rpx;
}

.used-card,
.expired-card {
  background: url('@/static/my/business/coupons_abnormal.png') no-repeat center center;
  background-size: cover;
}

// 优惠券主体部分
.coupon-main {
  position: relative;
  display: flex;
  height: 160rpx;
  min-height: 160rpx;
  background: transparent;

  .coupon-divider-vertical {
    height: 100% !important;
  }
}

:deep(.wd-divider) {
  margin: 0 4rpx !important;
}

// 优惠券左侧（金额区域）
.coupon-left {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 140rpx;
  padding: 20rpx 0;
  background: transparent;
  border-radius: 24rpx;
}

// 优惠券金额样式
.coupon-amount {
  display: flex;
  align-items: baseline;
  color: #ff4545;

  .currency {
    font-size: 28rpx;
  }

  .amount {
    margin-left: 4rpx;
    font-size: 52rpx;
    font-weight: normal;
  }
}

.used-amount,
.expired-amount {
  color: #555555;
}

// 优惠券右侧（信息区域）
.coupon-right {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx 20rpx 10rpx;
}

// 优惠券信息样式
.coupon-info {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;

  .coupon-title {
    width: 100%;
    margin-bottom: 16rpx;
    font-size: 32rpx;
    font-weight: normal;
    line-height: 1.3;
    color: #ff483c;
  }

  .coupon-desc,
  .coupon-used-jobs {
    display: inline-block;
    margin-right: 16rpx;
    font-size: 24rpx;
    font-weight: normal;
    line-height: 1.2;
    color: #888888;
  }

  .coupon-used-jobs {
    margin-right: 0;
  }
}

.used-info,
.expired-info {
  .coupon-title {
    color: #555555;
  }
}

// 优惠券操作区域样式
.coupon-action {
  margin-top: 0;
  margin-left: 20rpx;
}

// 使用按钮样式
:deep(.use-btn) {
  min-width: 156rpx !important;
  height: 65rpx !important;
  padding: 8rpx 24rpx !important;
  font-size: 22rpx !important;
  font-weight: normal !important;
  color: #ffffff !important;
  background: #ff4545 !important;
  border: none !important;
  border-radius: 65rpx !important;
}

// 有效期样式
.coupon-validity {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 58rpx;
  padding: 8rpx 20rpx 8rpx 48rpx;
  overflow: visible;
  font-size: 24rpx;
  font-weight: normal;
  color: #888888;
  text-align: left;
  background: transparent;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0rpx 2rpx 0rpx rgba(0, 0, 0, 0.1);
}
</style>
