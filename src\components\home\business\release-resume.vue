<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    :empty-view-super-style="{ justifyContent: 'flex-start' }"
    :fixed="false"
    :paging-style="pageStyle"
    auto-clean-list-when-reload
    show-refresher-when-reload
    @query="queryList"
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <view class="pr-62rpx pl-26rpx pb-32rpx pt-16rpx">
          <view
            class="content_list_left"
            style="display: flex; flex-direction: row; gap: 32rpx; align-items: flex-end"
          >
            <view
              v-for="(item, index) in jobStatusTabsList"
              :key="index"
              :class="{
                'type-tab-active': jobTabsStatus === item.name,
                'type-tab-inactive': jobTabsStatus !== item.name,
              }"
              class="content_list_left_for type-tab-item"
              @click="handleTabClick(index, item)"
            >
              <view class="type-tab-content">
                <view
                  :class="
                    jobTabsStatus === item.name
                      ? 'content_list_left_color'
                      : 'content_list_left_color1'
                  "
                  class="type-tab-text"
                >
                  {{ item.label }}
                </view>
                <view
                  :class="{
                    'indicator-visible': jobTabsStatus === item.name,
                    'indicator-hidden': jobTabsStatus !== item.name,
                  }"
                  class="type-tab-indicator"
                ></view>
              </view>
            </view>
            <view class="flex items-center gap-4rpx filter-button" @click="handleFilter">
              <text class="c-#555555 text-28rpx">筛选</text>
              <text class="i-carbon-triangle-down-solid text-14rpx c-#333333" />
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <template #empty>
      <view class="flex flex-col items-center justify-center m-t-200rpx">
        <wd-img :src="releasePostEmpty" height="412rpx" width="412rpx" />
        <text class="c-#000000 text-28rpx">
          {{ releaseIsHavePost ? '未匹配到求职者！' : '未发布岗位，无法查看简历' }}
        </text>
      </view>
    </template>
    <view class="p-[0_30rpx_44rpx] flex flex-col gap-20rpx">
      <personal-list
        v-for="(item, key) in pageData"
        :key="key"
        :list="item"
        :position="releaseActivePost"
      />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { EMIT_EVENT } from '@/enum'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { hrIndexResumeUserList } from '@/service/hrIndex'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import personalList from '@/components/common/personal-list.vue'
import releasePostEmpty from '@/static/home/<USER>/release-post-empty.png'
import { useResumeStore } from '@/store/resume'
import { useActivityStore } from '@/store/activity'

const resumeStore = useResumeStore()
const activityStore = useActivityStore()

defineOptions({
  name: 'HomeBusiness',
})

const { releaseActivePost, releaseIsHavePost } = useReleasePost()
const { pagingRef, pageInfo, pageSetInfo, pageData, pageStyle } =
  usePaging<hrIndexResumeUserListInt>({
    style: {},
  })
const jobTabsStatus = ref<Api.Common.EnableStatus>(0)
const jobStatusTabsList = [
  {
    label: '推荐',
    name: 0,
  },
  {
    label: '最新',
    name: 1,
  },
]
const themeVars: ConfigProviderThemeVars = {
  navbarHeight: '120rpx',
  tabsNavLineBgColor: '#FF9191',
  tabsNavHeight: '48rpx',
  tabsNavLineHeight: '4rpx',
  tabsNavLineWidth: '58rpx',
}
const handleFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index?type=toBusiness',
  })
}

async function fetchHrIndexResumeUserList() {
  const { data } = await hrIndexResumeUserList({
    entity: {
      positionInfoId: releaseActivePost.value.id,
      searchType: jobTabsStatus.value,
      salaryExpectationEnd: resumeStore?.fillterObg?.workSalaryEnd,
      salaryExpectationStart: resumeStore?.fillterObg?.workSalaryBegin,
      qualification: resumeStore?.fillterObg?.workEducational,
      jobType: resumeStore?.fillterObg?.jobType,
    },
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}

function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  if (page === 1) {
    Object.keys(activityStore.activityIncrements).forEach((userId) => {
      activityStore.activityIncrements[userId] = 0
    })
    Object.keys(activityStore.animationStates).forEach((userId) => {
      activityStore.animationStates[userId] = false
    })
  }
  fetchHrIndexResumeUserList()
}

async function reload() {
  pagingRef.value.reload()
}
function handleTabClick(_index: number, item: any) {
  jobTabsStatus.value = item.name
  reload()
}
uni.$on(EMIT_EVENT.REFRESH_HOME_RESUME, reload)
onBeforeUnmount(() => {
  uni.$off(EMIT_EVENT.REFRESH_HOME_RESUME)
})
defineExpose({
  reload,
})
</script>

<style lang="scss" scoped>
:deep(.zp-view-super) {
  margin: 0 !important;
}

:deep(.zp-paging-container-content) {
  height: auto !important;
}

:deep(.wd-navbar__left) {
  align-items: end;
}
/* 类型标签容器样式 */
.content_list_left_for {
  display: flex;
  flex-direction: row;
  //padding-right: 32rpx;
  border-bottom: 6rpx solid transparent;
}
/* 类型标签样式 */
.type-tab-item {
  position: relative;
  transition: none;
  transform: translateZ(0);

  &.type-tab-active {
    transform: translateZ(0);
  }

  &.type-tab-inactive {
    transform: translateZ(0);
  }
}

.type-tab-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.type-tab-text {
  font-size: 26rpx;
  transition:
    color 0.2s ease,
    font-size 0.2s ease;
  transform-origin: center;
}

.type-tab-indicator {
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  width: 58rpx;
  height: 6rpx;
  background: #ff9191;
  border-radius: 3rpx;
  transition: opacity 0.2s ease;
  transform: translateX(-50%);
  transform-origin: center;

  &.indicator-visible {
    opacity: 1;
  }

  &.indicator-hidden {
    opacity: 0;
  }
}

.content_list_left_color {
  font-size: 28rpx !important;
  font-weight: 600;
  color: #000000;
}

.content_list_left_color1 {
  font-size: 28rpx !important;
  font-weight: 400;
  color: #666666;
}

.content_list_left {
  display: flex;
  align-items: center;
}

.filter-button {
  padding-bottom: 6rpx;
}
</style>
