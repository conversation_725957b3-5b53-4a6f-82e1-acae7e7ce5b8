<template>
  <image :src="currentFrame" :class="imgClass" mode="aspectFit" @click="blinkOnce" />
</template>

<script setup lang="ts">
const frames = [
  '/static/character/normal.png',
  '/static/character/blink1.png',
  '/static/character/blink1.png',
  '/static/character/blink1.png',
  '/static/character/normal.png',
]

const modes = [
  {
    name: 'normal',
    interval: [2500, 4500],
    sequence: [0, 1, 2, 1, 0],
    delays: [0, 70, 120, 70, 0],
  },
  {
    name: 'sleepy',
    interval: [1200, 2800],
    sequence: [0, 1, 2, 2, 1, 0],
    delays: [0, 130, 200, 200, 130, 0],
  },
  {
    name: 'excited',
    interval: [600, 1600],
    sequence: [0, 1, 2, 1, 0, 1, 0],
    delays: [0, 40, 60, 40, 40, 40, 0],
  },
  {
    name: 'lazy',
    interval: [6000, 12000],
    sequence: [0, 1, 2, 1, 0],
    delays: [0, 200, 400, 200, 0],
  },
]
const imgClass = ['size-140rpx rounded-full block mx-auto select-none', 'transition-all']
const modeIndex = ref(1)
const currentFrame = ref(frames[0])
let autoTimer: number | null = null
let animTimer: number | null = null
let isAnimating = false

function blinkAnimation() {
  if (isAnimating) return
  isAnimating = true
  const mode = modes[modeIndex.value]
  const seq = mode.sequence
  const delays = mode.delays
  let i = 0
  function nextFrame() {
    currentFrame.value = frames[seq[i]]
    i++
    if (i < seq.length) {
      animTimer = setTimeout(nextFrame, delays[i])
    } else {
      isAnimating = false
    }
  }
  nextFrame()
}

function scheduleBlink() {
  if (autoTimer) clearTimeout(autoTimer)
  const mode = modes[modeIndex.value]
  const delay = Math.random() * (mode.interval[1] - mode.interval[0]) + mode.interval[0]
  autoTimer = setTimeout(() => {
    blinkAnimation()
    scheduleBlink()
  }, delay)
}

function blinkOnce() {
  if (autoTimer) clearTimeout(autoTimer)
  blinkAnimation()
  scheduleBlink()
}

onMounted(() => {
  scheduleBlink()
})

onUnmounted(() => {
  if (autoTimer) clearTimeout(autoTimer)
  if (animTimer) clearTimeout(animTimer)
})
</script>
