<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="附件简历"></CustomNavBar>
    </template>
    <view class="box">
      <view class="box-list">
        <view
          class="box-list-item flex-between m-b-30rpx"
          v-for="(item, index) in fileList"
          :key="index"
          style="position: relative"
        >
          <view class="flex-c" @click="previewPdf(item)" style="width: 100%">
            <wd-img :width="38" :height="38" :src="pdf" />
            <view class="m-l-30rpx">
              <view class="text-28rpx c-#333333 p-b-10rpx">{{ item.fileName }}</view>
              <view class="text-24rpx c-#888888">{{ item.createTime }}</view>
            </view>
          </view>
          <wd-img
            :src="del"
            @click.stop="delpdf(item.id)"
            style="
              position: absolute;
              top: 50%;
              right: 40rpx;
              z-index: 2;
              width: 64rpx;
              height: 64rpx;
              padding: 14rpx;
              transform: translateY(-50%);
            "
          ></wd-img>
        </view>
        <view class="box-list-item flex-between m-b-30rpx" @click="toWebViewUpload">
          <view class="text-28rpx c-#333333">上传新附件({{ fileList.length }}/3)</view>
          <wd-img :width="20" :height="20" :src="addpdf"></wd-img>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="GenerateResume">生成简历附件</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import pdf from '@/resumeRelated/img/pdfimg.png'
import del from '@/resumeRelated/img/del.png'
import addpdf from '@/resumeRelated/img/addpdf.png'
import { queryMyFileResumeList, deleteFileResume } from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
import { baseUrlPdfPrever } from '@/interPost/img/index'
const { getToken } = useUserInfo()
const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 生成简历
const GenerateResume = () => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/GeneratePDF`,
  })
}

onShow(() => {
  getList()
})

const fileList = ref([])
// 获取列表
const getList = async () => {
  const res: any = await queryMyFileResumeList()
  console.log(res, 'res==============')
  if (res.code === 0) {
    fileList.value = res.data
  }
}
// 查看pdf
const previewPdf = (item: any) => {
  // 跳转到 WebViewpdf.vue 页面，fileUrl 作为参数传递
  // const fileUrl = encodeURIComponent(item.fileUrl)
  // console.log(item, 'item==============')
  // uni.navigateTo({
  //   url: `${baseUrlPdfPrever}?fileUrl=${item.fileUrl}`,
  // })
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewpdf?fileUrl=${item.fileUrl}`,
  })
}
// 删除
const delpdf = async (id: any) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      deleteFileResume({ id }).then((res: any) => {
        if (res.code === 0) {
          getList()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}

// 跳转到WebView上传页面
const toWebViewUpload = () => {
  if (fileList.value.length >= 3) {
    uni.showToast({ title: '最多上传3个附件', icon: 'none' })
    return
  }

  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewUpload?type=portfolio`,
  })
}

// 监听上传成功事件
onMounted(() => {
  uni.$on('pdfUploadSuccess', getList)
})

onUnmounted(() => {
  uni.$off('pdfUploadSuccess', getList)
})

const addPdfImg3 = () => {
  uni.showToast({ title: '最多上传3个附件', icon: 'none' })
}
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
