import {
  PermissionType,
  PermissionStatus,
  PermissionResult,
  ANDROID_PERMISSIONS,
  IOS_PERMISSION_STATUS,
} from '@/types/permission'
import { permissionTipManager } from './permissionTip'

function isPlusEnv(): boolean {
  return typeof plus !== 'undefined'
}

class PermissionManager {
  private isIOS: boolean = false

  constructor() {
    if (isPlusEnv()) {
      this.isIOS = plus.os.name === 'iOS'
    }
  }

  /**
   * 检查权限状态
   * @param permissionType 权限类型
   * @returns 权限检查结果
   */
  async checkPermission(permissionType: PermissionType): Promise<PermissionResult> {
    try {
      if (this.isIOS) {
        const granted = this.checkIOSPermission(permissionType)
        return {
          granted,
          status: granted ? PermissionStatus.GRANTED : PermissionStatus.DENIED,
        }
      } else {
        console.log('检查Android权限:', permissionType)
        // 先检查权限状态
        const currentStatus = this.checkAndroidPermissionStatus(permissionType)
        if (currentStatus === PermissionStatus.GRANTED) {
          console.log(`Android权限已授予: ${permissionType}`)
          return {
            granted: true,
            status: PermissionStatus.GRANTED,
          }
        }
        permissionTipManager.showPermissionTip(permissionType)
        const result = await this.requestAndroidPermission(permissionType)
        permissionTipManager.hideTip()
        console.log(`Android权限检查结果: ${result}`)
        return {
          granted: result === PermissionStatus.GRANTED,
          status: result as PermissionStatus,
        }
      }
    } catch (error) {
      console.error(`检查${permissionType}权限失败:`, error)
      return {
        granted: false,
        status: PermissionStatus.DENIED,
        message: error instanceof Error ? error.message : '权限检查失败',
      }
    }
  }

  /**
   * 检查Android权限状态（针对相册权限优化）
   */
  private checkAndroidPermissionStatus(permissionType: PermissionType): PermissionStatus {
    if (!isPlusEnv()) return PermissionStatus.GRANTED

    const permissionID = ANDROID_PERMISSIONS[permissionType]
    if (!permissionID) {
      console.warn(`Android平台不支持${permissionType}权限检查`)
      return PermissionStatus.GRANTED
    }

    try {
      const context = plus.android.runtimeMainActivity()
      const PackageManager = plus.android.importClass('android.content.pm.PackageManager')

      // 对于相册权限，需要特殊处理
      if (permissionType === 'photoLibrary') {
        // 检查Android版本
        const Build = plus.android.importClass('android.os.Build')
        // @ts-expect-error app支持这样写
        const sdkVersion = Build.VERSION.SDK_INT

        if (sdkVersion >= 33) {
          // Android 13+
          const mediaImagesPermission = 'android.permission.READ_MEDIA_IMAGES'
          // @ts-expect-error app支持这样写
          const result = context.checkSelfPermission(mediaImagesPermission)
          // @ts-expect-error app支持这样写
          const isGranted = result === PackageManager.PERMISSION_GRANTED

          console.log(
            `Android ${sdkVersion} 相册权限(READ_MEDIA_IMAGES)状态: ${isGranted ? '已授予' : '未授予'}`,
          )
          return isGranted ? PermissionStatus.GRANTED : PermissionStatus.DENIED
        } else {
          // Android 13以下使用原有的权限
          // @ts-expect-error app支持这样写
          const result = context.checkSelfPermission(permissionID)
          // @ts-expect-error app支持这样写
          const isGranted = result === PackageManager.PERMISSION_GRANTED

          console.log(
            `Android ${sdkVersion} 相册权限(READ_EXTERNAL_STORAGE)状态: ${isGranted ? '已授予' : '未授予'}`,
          )
          return isGranted ? PermissionStatus.GRANTED : PermissionStatus.DENIED
        }
      }

      // 其他权限的检查逻辑
      // @ts-expect-error app支持这样写
      const result = context.checkSelfPermission(permissionID)
      // @ts-expect-error app支持这样写
      const isGranted = result === PackageManager.PERMISSION_GRANTED

      if (isGranted) {
        return PermissionStatus.GRANTED
      }

      // @ts-expect-error app支持这样写
      const shouldShowRationale = context.shouldShowRequestPermissionRationale(permissionID)
      console.log(`权限${permissionType}状态: 未授予, shouldShowRationale: ${shouldShowRationale}`)

      return PermissionStatus.DENIED
    } catch (error) {
      console.error(`检查Android权限状态失败:`, error)
      return PermissionStatus.DENIED
    }
  }

  /**
   * 检查iOS权限
   */
  private checkIOSPermission(permissionType: PermissionType): boolean {
    switch (permissionType) {
      case 'push':
        return this.checkIOSPushPermission()
      case 'location':
        return this.checkIOSLocationPermission()
      case 'record':
        return this.checkIOSRecordPermission()
      case 'camera':
        return this.checkIOSCameraPermission()
      case 'photoLibrary':
        return this.checkIOSPhotoLibraryPermission()
      case 'contact':
        return this.checkIOSContactPermission()
      case 'calendar':
        return this.checkIOSCalendarPermission()
      case 'memo':
        return this.checkIOSMemoPermission()
      default:
        return false
    }
  }

  /**
   * iOS推送权限检查
   */
  private checkIOSPushPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const UIApplication = plus.ios.importClass('UIApplication')
      const app = UIApplication.sharedApplication()
      let enabledTypes = 0
      let result = false

      if (app.currentUserNotificationSettings) {
        const settings = app.currentUserNotificationSettings()
        enabledTypes = settings.plusGetAttribute('types')
        result = enabledTypes !== IOS_PERMISSION_STATUS.PUSH_DENIED
        plus.ios.deleteObject(settings)
      } else {
        enabledTypes = app.enabledRemoteNotificationTypes()
        result = enabledTypes !== IOS_PERMISSION_STATUS.PUSH_DENIED
      }

      plus.ios.deleteObject(app)
      plus.ios.deleteObject(UIApplication)

      console.log(`推送权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS推送权限失败:', error)
      return false
    }
  }

  /**
   * iOS定位权限检查
   */
  private checkIOSLocationPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const CLLocationManager = plus.ios.importClass('CLLocationManager')
      const status = CLLocationManager.authorizationStatus()
      const result = status !== IOS_PERMISSION_STATUS.LOCATION_DENIED

      plus.ios.deleteObject(CLLocationManager)
      console.log(`定位权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS定位权限失败:', error)
      return false
    }
  }

  /**
   * iOS麦克风权限检查
   */
  private checkIOSRecordPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const AVAudioSession = plus.ios.importClass('AVAudioSession')
      const audioSession = AVAudioSession.sharedInstance()
      const permissionStatus = audioSession.recordPermission()

      const result = !IOS_PERMISSION_STATUS.RECORD_DENIED.includes(permissionStatus)

      plus.ios.deleteObject(AVAudioSession)
      console.log(`麦克风权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS麦克风权限失败:', error)
      return false
    }
  }

  /**
   * iOS相机权限检查
   */
  private checkIOSCameraPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const AVCaptureDevice = plus.ios.importClass('AVCaptureDevice')
      const authStatus = AVCaptureDevice.authorizationStatusForMediaType('video')
      const result = authStatus === IOS_PERMISSION_STATUS.CAMERA_GRANTED
      plus.ios.deleteObject(AVCaptureDevice)
      console.log(`相机权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS相机权限失败:', error)
      return false
    }
  }

  /**
   * iOS相册权限检查
   */
  private checkIOSPhotoLibraryPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const PHPhotoLibrary = plus.ios.importClass('PHPhotoLibrary')
      const authStatus = PHPhotoLibrary.authorizationStatus()
      const result = authStatus === IOS_PERMISSION_STATUS.PHOTO_GRANTED

      plus.ios.deleteObject(PHPhotoLibrary)
      console.log(`相册权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS相册权限失败:', error)
      return false
    }
  }

  /**
   * iOS通讯录权限检查
   */
  private checkIOSContactPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const CNContactStore = plus.ios.importClass('CNContactStore')
      const authStatus = CNContactStore.authorizationStatusForEntityType(0)
      const result = authStatus === IOS_PERMISSION_STATUS.CONTACT_GRANTED

      plus.ios.deleteObject(CNContactStore)
      console.log(`通讯录权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS通讯录权限失败:', error)
      return false
    }
  }

  /**
   * iOS日历权限检查
   */
  private checkIOSCalendarPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const EKEventStore = plus.ios.importClass('EKEventStore')
      const authStatus = EKEventStore.authorizationStatusForEntityType(0)
      const result = authStatus === IOS_PERMISSION_STATUS.CALENDAR_GRANTED

      plus.ios.deleteObject(EKEventStore)
      console.log(`日历权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS日历权限失败:', error)
      return false
    }
  }

  /**
   * iOS备忘录权限检查
   */
  private checkIOSMemoPermission(): boolean {
    if (!isPlusEnv()) return true
    try {
      const EKEventStore = plus.ios.importClass('EKEventStore')
      const authStatus = EKEventStore.authorizationStatusForEntityType(1)
      const result = authStatus === IOS_PERMISSION_STATUS.CALENDAR_GRANTED

      plus.ios.deleteObject(EKEventStore)
      console.log(`备忘录权限状态: ${result ? '已开启' : '未开启'}`)
      return result
    } catch (error) {
      console.error('检查iOS备忘录权限失败:', error)
      return false
    }
  }

  /**
   * Android权限请求
   */
  private requestAndroidPermission(permissionType: PermissionType): Promise<PermissionStatus> {
    return new Promise((resolve) => {
      if (!isPlusEnv()) {
        resolve(PermissionStatus.GRANTED)
        return
      }

      let permissionID: string = ANDROID_PERMISSIONS[permissionType]

      // 相册权限特殊处理
      if (permissionType === 'photoLibrary') {
        try {
          const Build = plus.android.importClass('android.os.Build')
          // @ts-expect-error app支持这样写
          const sdkVersion = Build.VERSION.SDK_INT
          if (sdkVersion >= 33) {
            permissionID = 'android.permission.READ_MEDIA_IMAGES'
            console.log('使用Android 13+的相册权限: READ_MEDIA_IMAGES')
          } else {
            console.log('使用传统相册权限: READ_EXTERNAL_STORAGE')
          }
        } catch (error) {
          console.error('获取Android版本失败，使用默认权限')
        }
      }

      if (!permissionID) {
        console.warn(`Android平台不支持${permissionType}权限检查`)
        resolve(PermissionStatus.GRANTED)
        return
      }

      console.log(`请求权限: ${permissionID}`)

      plus.android.requestPermissions(
        [permissionID],
        (resultObj) => {
          console.log('权限请求结果:', resultObj)

          if (resultObj.granted.length > 0) {
            console.log(`${permissionType}权限已获取`)
            resolve(PermissionStatus.GRANTED)
          } else if (resultObj.deniedPresent.length > 0) {
            console.log(`${permissionType}权限被拒绝`)
            resolve(PermissionStatus.DENIED)
          } else if (resultObj.deniedAlways.length > 0) {
            console.log(`${permissionType}权限被永久拒绝`)
            resolve(PermissionStatus.DENIED)
          } else {
            resolve(PermissionStatus.DENIED)
          }
        },
        (error) => {
          console.error(`申请${permissionType}权限错误:`, error)
          resolve(PermissionStatus.DENIED)
        },
      )
    })
  }

  /**
   * 检查系统定位服务是否开启
   */
  checkSystemLocationEnabled(): boolean {
    if (!isPlusEnv()) return true

    if (this.isIOS) {
      try {
        const CLLocationManager = plus.ios.importClass('CLLocationManager')
        const result = CLLocationManager.locationServicesEnabled()
        plus.ios.deleteObject(CLLocationManager)
        console.log(`系统定位服务状态: ${result ? '已开启' : '未开启'}`)
        return result
      } catch (error) {
        console.error('检查iOS系统定位服务失败:', error)
        return false
      }
    } else {
      try {
        const context = plus.android.importClass('android.content.Context')
        const locationManager = plus.android.importClass('android.location.LocationManager')
        const main = plus.android.runtimeMainActivity()
        // @ts-expect-error app支持这样写
        const mainSvr = main.getSystemService(context.LOCATION_SERVICE)
        // @ts-expect-error app支持这样写
        const result = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)
        return result
      } catch (error) {
        console.error('检查Android系统定位服务失败:', error)
        return false
      }
    }
  }

  /**
   * 跳转到应用权限设置页面
   */
  gotoAppPermissionSetting() {
    if (!isPlusEnv()) return

    if (this.isIOS) {
      try {
        const UIApplication = plus.ios.importClass('UIApplication')
        const application = UIApplication.sharedApplication()
        const NSURL = plus.ios.importClass('NSURL')
        const settingURL = NSURL.URLWithString('app-settings:')
        application.openURL(settingURL)
        plus.ios.deleteObject(settingURL)
        plus.ios.deleteObject(NSURL)
        plus.ios.deleteObject(application)
        plus.ios.deleteObject(UIApplication)
      } catch (error) {
        console.error('跳转iOS权限设置失败:', error)
      }
    } else {
      try {
        const Intent = plus.android.importClass('android.content.Intent')
        const Settings = plus.android.importClass('android.provider.Settings')
        const Uri = plus.android.importClass('android.net.Uri')
        const mainActivity = plus.android.runtimeMainActivity()
        // @ts-expect-error app支持这样写
        const intent = new Intent()
        // @ts-expect-error app支持这样写
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        // @ts-expect-error app支持这样写
        const uri = Uri.fromParts('package', mainActivity.getPackageName(), null)
        intent.setData(uri)
        // @ts-expect-error app支持这样写
        mainActivity.startActivity(intent)
      } catch (error) {
        console.error('跳转Android权限设置失败:', error)
      }
    }
  }
}
export const permissionManager = new PermissionManager()
export default permissionManager
