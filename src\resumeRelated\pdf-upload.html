<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF上传</title>
    <script src="./webview.js"></script>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 20px;
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      .upload-container {
        width: 100%;
        max-width: 500px;
        padding: 30px;
        margin-top: -200px;
        text-align: center;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }
      .upload-icon {
        margin-bottom: 20px;
        font-size: 64px;
        color: #5e7ce0;
      }
      h2 {
        margin-bottom: 10px;
        font-size: 22px;
        color: #333;
      }
      p {
        margin-bottom: 30px;
        font-size: 16px;
        color: #666;
      }
      .upload-area {
        padding: 0px 20px 40px;
        margin-bottom: 25px;
        background: #fafbff;
        border: 2px dashed #ddd;
        border-radius: 12px;
        transition: all 0.3s;
      }
      .upload-area.active {
        background: #f0f4ff;
        border-color: #5e7ce0;
      }
      .upload-area i {
        display: block;
        margin-bottom: 15px;
        font-size: 48px;
        color: #5e7ce0;
      }
      .upload-btn {
        display: inline-block;
        padding: 12px 30px;
        margin-top: 10px;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background: linear-gradient(90deg, #5e7ce0, #8a6cef);
        border: none;
        border-radius: 8px;
        outline: none;
        transition: all 0.3s;
      }
      .upload-btn:active {
        transform: scale(0.98);
      }
      .file-input {
        display: none;
      }
      .file-info {
        padding: 15px;
        margin-top: 20px;
        text-align: left;
        background: #f9f9f9;
        border-radius: 8px;
      }
      .file-name {
        font-size: 14px;
        color: #333;
        word-break: break-all;
      }
      .file-size {
        margin-top: 5px;
        font-size: 13px;
        color: #888;
      }
      .upload-status {
        margin: 25px 0;
        font-size: 14px;
        color: #888;
      }
      .progress-container {
        height: 6px;
        margin: 15px 0;
        overflow: hidden;
        background: #eee;
        border-radius: 3px;
      }
      .progress-bar {
        width: 0%;
        height: 100%;
        background: linear-gradient(90deg, #5e7ce0, #8a6cef);
        transition: width 0.3s;
      }
      .btn-group {
        display: flex;
        gap: 15px;
        margin-top: 20px;
      }
      .cancel-btn {
        flex: 1;
        padding: 12px;
        font-size: 16px;
        color: #666;
        cursor: pointer;
        background: #eee;
        border: none;
        border-radius: 8px;
      }
      .confirm-btn {
        flex: 1;
        padding: 12px;
        font-size: 16px;
        color: white;
        cursor: pointer;
        background: linear-gradient(90deg, #5e7ce0, #8a6cef);
        border: none;
        border-radius: 8px;
      }
      .error-message {
        margin-top: 15px;
        font-size: 14px;
        color: #ff4d4f;
      }
      .close-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        font-size: 24px;
        color: #999;
        cursor: pointer;
      }
    </style>
  </head>
  <body>
    <div class="upload-container">
      <div class="upload-icon">📄</div>
      <h2>上传PDF简历</h2>
      <p>支持PDF格式文件，最大3MB</p>

      <div class="upload-area" id="uploadArea">
        <i>📁</i>
        <button class="upload-btn" id="selectBtn">选择PDF文件</button>
        <!-- 修复1: 使用正确的accept类型 -->
        <input type="file" id="pdfFile" class="file-input" accept="application/pdf" />
      </div>

      <div class="file-info" id="fileInfo" style="display: none">
        <div class="file-name" id="fileName"></div>
        <div class="file-size" id="fileSize"></div>
      </div>

      <div class="upload-status" id="uploadStatus" style="display: none">
        <div>上传中...</div>
        <div class="progress-container">
          <div class="progress-bar" id="progressBar"></div>
        </div>
        <div id="progressText">0%</div>
      </div>

      <div class="btn-group" id="btnGroup" style="display: none">
        <button class="cancel-btn" id="cancelBtn">取消</button>
        <button class="confirm-btn" id="uploadBtn">开始上传</button>
      </div>

      <div class="error-message" id="errorMessage"></div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', () => {
        // 获取DOM元素
        const uploadArea = document.getElementById('uploadArea')
        const selectBtn = document.getElementById('selectBtn')
        const fileInput = document.getElementById('pdfFile')
        const fileInfo = document.getElementById('fileInfo')
        const fileName = document.getElementById('fileName')
        const fileSize = document.getElementById('fileSize')
        const uploadStatus = document.getElementById('uploadStatus')
        const progressBar = document.getElementById('progressBar')
        const progressText = document.getElementById('progressText')
        const btnGroup = document.getElementById('btnGroup')
        const uploadBtn = document.getElementById('uploadBtn')
        const cancelBtn = document.getElementById('cancelBtn')
        const errorMessage = document.getElementById('errorMessage')

        // 当前选中的文件
        let selectedFile = null
        let xhr = null // 用于取消上传

        // 获取URL中的token参数
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token') || ''
        const baseUrl = urlParams.get('baseUrl') || ''

        // 选择文件按钮
        selectBtn.addEventListener('click', () => {
          fileInput.click()
        })

        // 拖放功能
        uploadArea.addEventListener('dragover', (e) => {
          e.preventDefault()
          uploadArea.classList.add('active')
        })

        uploadArea.addEventListener('dragleave', () => {
          uploadArea.classList.remove('active')
        })

        uploadArea.addEventListener('drop', (e) => {
          e.preventDefault()
          uploadArea.classList.remove('active')

          if (e.dataTransfer.files.length) {
            handleFileSelect(e.dataTransfer.files[0])
          }
        })

        // 文件选择处理
        fileInput.addEventListener('change', (e) => {
          if (e.target.files.length) {
            handleFileSelect(e.target.files[0])
          }
        })

        // 处理文件选择
        function handleFileSelect(file) {
          // 验证文件类型
          if (file.type !== 'application/pdf') {
            showError('请上传PDF格式文件')
            return
          }

          // 验证文件大小
          if (file.size > 3 * 1024 * 1024) {
            showError('文件大小不能超过3MB')
            return
          }

          // 显示文件信息
          selectedFile = file
          fileName.textContent = file.name
          fileSize.textContent = formatFileSize(file.size)
          fileInfo.style.display = 'block'
          btnGroup.style.display = 'flex'
          errorMessage.textContent = ''
        }

        // 取消上传
        cancelBtn.addEventListener('click', () => {
          if (xhr) {
            xhr.abort()
            resetUI()
          } else {
            resetUI()
          }
        })

        // 开始上传
        uploadBtn.addEventListener('click', () => {
          if (!selectedFile) return

          // 准备上传
          uploadStatus.style.display = 'block'
          btnGroup.style.display = 'none'
          errorMessage.textContent = ''

          const formData = new FormData()
          formData.append('file', selectedFile)

          xhr = new XMLHttpRequest()

          // 上传进度处理
          xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
              const percent = Math.round((e.loaded / e.total) * 100)
              progressBar.style.width = percent + '%'
              progressText.textContent = percent + '%'
            }
          })

          // 上传完成
          xhr.addEventListener('load', () => {
            console.log('xhr.status========================', xhr.status)
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText)
                console.log('response========================', response)

                if (response.code === 0) {
                  console.log('response=====', response)
                  console.log('response=====', response.data)
                  // 上传成功
                  uni.postMessage({
                    data: {
                      action: 'uploadSuccess',
                      fileId: response.data,
                    },
                  })
                } else {
                  showError(response.msg || '上传失败')
                  resetUI()
                }
              } catch (e) {
                showError('解析响应失败')
                resetUI()
              }
            } else {
              showError('上传失败: ' + '请重试')
              resetUI()
            }
          })

          // 错误处理
          xhr.addEventListener('error', () => {
            showError('网络错误，请重试')
            resetUI()
          })

          // 中止处理
          xhr.addEventListener('abort', () => {
            showError('上传已取消')
            resetUI()
          })

          // 修复2: 确保使用完整API路径
          const apiUrl = baseUrl
          console.log('API URL:', apiUrl)
          console.log('Token:', token)
          xhr.open('POST', apiUrl, true)
          xhr.setRequestHeader('token', token)
          xhr.send(formData)
        })

        // 重置UI状态
        function resetUI() {
          selectedFile = null
          fileInfo.style.display = 'none'
          uploadStatus.style.display = 'none'
          btnGroup.style.display = 'none'
          progressBar.style.width = '0%'
          progressText.textContent = '0%'
          fileInput.value = ''
          xhr = null
        }

        // 显示错误信息
        function showError(msg) {
          console.log('msg========================', msg)
          errorMessage.textContent = msg
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
          if (bytes === 0) return '0 Bytes'
          const k = 1024
          const sizes = ['Bytes', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i]
        }
      })
    </script>
  </body>
</html>
