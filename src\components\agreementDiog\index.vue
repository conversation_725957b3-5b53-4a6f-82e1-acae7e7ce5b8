<template>
  <wd-popup v-model="localShow" @close="handleClose">
    <view class="w-100vw h-100vh bg-#fff p-40rpx relative">
      <z-paging ref="paging" :paging-style="pageStyle" layout-only>
        <template #top>
          <CustomNavBar>
            <template #left>
              <view class=""></view>
            </template>
          </CustomNavBar>
        </template>
        <view class="px-40rpx p-b-40rpx">
          <view class="flex justify-center items-center">
            <wd-img :width="74" :height="74" :src="logo" />
          </view>
          <view class="text-34rpx text-center py-30rpx c-#000 font-bold">个人信息保护指引</view>
          <view class="text-28rpx text-center c-#000 text-left indent-60rpx line-height-50rpx">
            欢迎使用易直聘我们致力于为您打造安全、合规的求职招聘环境，依据招聘类APP
            合规要求，将采取严格且必要的安全措施保护您的信息安全。
            在您使用本应用过程中，我们会因业务功能需要，向您申请定位、相机、相册、日历、麦克风、通知等权限。这些权限不会默认开启，当您实际使用对应业务功能（如发布带定位的求职/招聘信息、拍摄上传简历附件等
            ）时，我们会再次弹窗，明确征得您同意后才会启用。同时，我们会收集您的设备信息、日志信息，用于信息发布合规性审核、打击虚假招聘/求职等违法违规行为，以及防止平台用户个人信息泄露，保障您的合法权益。
          </view>
          <view class="text-28rpx text-center c-#000 text-left indent-60rpx line-height-50rpx">
            根据相关法律法规及行业规范，我们仅向16 周岁及以上用户提供求职招聘服务。若您未满 16
            周岁，请勿以任何形式使用本服务；若我们发现无意中收集了未满 16
            周岁人士的个人信息，会立即依据适用法律法规删除，并采取合理补救措施。
          </view>
          <view class="text-28rpx text-center c-#000 text-left indent-60rpx line-height-50rpx">
            点击【同意】，即视为您已阅读并同意易直聘
            <text class="c-#0547FF" @click="uesrAgreement">《用户协议》</text>
            和
            <text class="c-#0547FF" @click="privacyAgreement">《隐私政策》</text>
            及上述内容，您可自主选择享受我们为您提供的专业求职招聘服务；若您点击【拒绝】，将无法使用易直聘完整功能，感谢您的理解与支持。
          </view>
        </view>
        <template #bottom>
          <view class="flex justify-center items-center p-t-40rpx p-b-40rpx">
            <view
              class="w-300rpx bg-#C2C2C2 text-center py-20rpx rounded-60rpx c-#fff m-r-40rpx"
              @click="handleRefuse"
            >
              拒绝
            </view>
            <view
              @click="handleAgree"
              class="w-300rpx text-center py-20rpx rounded-60rpx c-#000 bg-gradient-linear bg-gradient-[90deg,#FFC2C2_0%,#DDDCFF_100%]"
            >
              同意
            </view>
          </view>
        </template>
      </z-paging>
    </view>
  </wd-popup>
  <wd-message-box class="custom-shadow" />
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useMessage } from 'wot-design-uni'
import logo from '@/static/images/login/logo.png'
import { setUserHasShownAgreement } from '@/utils/storage'
const { initEaseMobIM } = useEaseMobIM()
const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:show', 'agreePrivacyLogin'])
// 新增本地变量 localShow
const localShow = ref(props.show)
// 监听 props.show 变化同步 localShow
watch(
  () => props.show,
  (val) => {
    localShow.value = val
  },
)
// 监听 localShow 变化，通知父组件
watch(localShow, (val) => {
  emit('update:show', val)
})
const uesrAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/ResumeUser',
  })
}
const privacyAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/PrivacyPolicy',
  })
}
const handleRefuse = () => {
  console.log('不同意并退出')
  message
    .confirm({
      title: '温馨提示',
      msg: '您可以选择退出应用或继续使用游客模式',
      confirmButtonText: '游客模式',
      cancelButtonText: '不同意',
    })
    .then(() => {
      uni.navigateTo({
        url: '/setting/tourist/index',
      })
      console.log('继续使用游客模式')
    })
    .catch(() => {
      // plus.runtime.quit()
      console.log('不同意并退出')
    })
}
const handleClose = () => {
  localShow.value = false
}
// 同意
const handleAgree = () => {
  emit('agreePrivacyLogin')
  initEaseMobIM()
  setUserHasShownAgreement(true)
  localShow.value = false
}
</script>

<style lang="scss" scoped>
.custom-shadow {
  z-index: 10001 !important;
}

:deep(.wd-button__content) {
  font-size: 24rpx !important;
}
</style>
