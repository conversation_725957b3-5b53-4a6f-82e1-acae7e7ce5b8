<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-navbar
        left-arrow
        :bordered="false"
        safe-area-inset-top
        custom-class="!bg-transparent"
        @click-left="handleClickLeft"
      />
    </template>
    <view class="mt-66rpx flex flex-col items-center gap-44rpx">
      <view class="flex items-center relative ml-60rpx">
        <view
          class="absolute left--60rpx shadow-[0rpx_22rpx_43rpx_0rpx_rgba(0,0,0,0.3)] size-121rpx rounded-full overflow-hidden"
        >
          <wd-img :src="userInfo.avatar" height="121rpx" width="121rpx" />
        </view>
        <view
          class="flex flex-col justify-center gap-10rpx w-433rpx h-144rpx bg-white shadow-[0rpx_36rpx_58rpx_0rpx_rgba(0,0,0,0.03)] rounded-35rpx pl-88rpx"
        >
          <text class="c-#333333 text-28rpx">{{ userInfo?.name }}</text>
        </view>
      </view>
      <wd-config-provider :themeVars="themeVars">
        <view
          class="bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx w-640rpx px-30rpx py-25rpx"
        >
          <view class="py-25rpx flex items-center">
            <view class="flex-1">
              <text class="c-#333333 text-28rpx">置顶联系人</text>
            </view>
            <wd-switch
              v-model="isTopContact"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)]"
            />
          </view>
          <view class="py-25rpx flex items-center">
            <view class="flex-1 flex flex-col gap-6rpx">
              <text class="c-#333333 text-28rpx">加入黑名单</text>
              <text class="c-#888888 text-22rpx">加入黑名单后，将不再接受对方发送的消息</text>
            </view>
            <wd-switch
              v-model="isBlacklisted"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)]"
            />
          </view>
          <view class="py-25rpx flex items-center">
            <view class="flex-1">
              <text class="c-#333333 text-28rpx">
                {{ uninterestedMsg }}
              </text>
            </view>
            <wd-switch
              v-model="isUninterested"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)]"
            />
          </view>
        </view>
      </wd-config-provider>
      <view
        class="flex items-center w-640rpx h-118rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx px-30rpx"
        @click="handleReport"
      >
        <text class="flex-1 c-#333333 text-28rpx">举报对方</text>
        <wd-icon name="arrow-right" color="#333333" size="30rpx" />
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { hrUnInterestUserAdd, hrUnInterestUserCancel } from '@/service/hrUnInterestUser'
import { unInterestPositionAdd, unInterestPositionCancel } from '@/service/unInterestPosition'
import { imBlackListAdd, imBlackListDelete } from '@/service/imBlackList'

const { pageStyle } = usePaging({
  style: {
    background: '#F0F0F0',
  },
})
const toast = useToast()
const { userRoleIsBusiness } = useUserInfo()
const {
  loadConversations,
  getLocalConversationById,
  updateBlacklistUsers,
  unInterestBlackUserNameList,
  getConversationInfo,
  deleteConversation,
  clearConversationSilentMode,
  toggleImUserBlockList,
  isUserInImBlockList,
  customCardInfo,
} = useIMConversation()
const themeVars: ConfigProviderThemeVars = {
  switchWidth: '100rpx',
  switchHeight: '40rpx',
  switchCircleSize: '46rpx',
}
const uninterestedMsg = computed(() => {
  return userRoleIsBusiness.value ? '不合适' : '不感兴趣'
})
const userInfo = computed(() => {
  const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(
    getConversationInfo.value.conversationId,
  )
  return {
    name: userInfo?.name,
    id: getConversationInfo.value.conversationId,
    avatar: userInfo?.avatar,
    conversationType: getConversationInfo.value.conversationType,
    presenceExt: userInfo?.presenceExt,
    isOnline: userInfo?.isOnline,
    ext: JSON.parse(userInfo?.ext || '{}') as Api.IM.UserBusinessExtInfo,
  }
})
const imCustomUserId = computed(() => {
  return userRoleIsBusiness.value ? userInfo.value?.ext?.cUserId : userInfo.value?.ext?.hrUserId
})
const isBlacklisted = computed({
  get() {
    return isUserInImBlockList(getConversationInfo.value.conversationId)
  },
  async set(bool: boolean) {
    const conversation = getLocalConversationById(getConversationInfo.value.conversationId)
    if (!conversation) {
      toast.show('会话不存在')
      return
    }
    uni.showLoading({
      title: bool ? '加入黑名单' : '移除黑名单',
      mask: true,
    })
    try {
      await toggleImUserBlockList([getConversationInfo.value.conversationId], bool)
      if (!bool) {
        await imBlackListDelete(
          {
            blackUserName: getConversationInfo.value.conversationId,
          },
          {
            custom: {
              catch: true,
            },
          },
        )
      } else {
        await imBlackListAdd(
          {
            blackUserName: getConversationInfo.value.conversationId,
          },
          {
            custom: {
              catch: true,
            },
          },
        )
      }
    } catch (error) {
      console.error('黑名单操作失败:', error)
      toast.error('操作失败，请稍后再试')
    } finally {
      uni.hideLoading()
    }
  },
})
const isTopContact = computed({
  get() {
    const conversation = getLocalConversationById(getConversationInfo.value.conversationId)
    return conversation?.isPinned || false
  },
  async set(bool: boolean) {
    const conversation = getLocalConversationById(getConversationInfo.value.conversationId)
    if (!conversation) {
      toast.show('会话不存在')
      return
    }
    uni.showLoading({
      title: bool ? '置顶联系人' : '取消置顶',
      mask: true,
    })
    try {
      uni.$UIKit.convStore.pinConversation(conversation, bool)
      await loadConversations(true)
    } catch {
    } finally {
      uni.hideLoading()
    }
  },
})
const isUninterested = computed({
  get() {
    return unInterestBlackUserNameList.value.includes(userInfo.value?.id)
  },
  async set(bool: boolean) {
    uni.showLoading({
      title: `${bool ? '添加' : '取消'}${uninterestedMsg.value}`,
      mask: true,
    })
    if (bool) {
      if (userRoleIsBusiness.value) {
        await hrUnInterestUserAdd({
          userId: imCustomUserId.value,
        })
      } else {
        await unInterestPositionAdd({
          positionId: customCardInfo.value.positionInfoId,
        })
      }
      await deleteConversation(getConversationInfo.value.conversationId)
      setTimeout(() => {
        uni.navigateBack({
          delta: 2,
        })
      }, 500)
    } else {
      if (userRoleIsBusiness.value) {
        await hrUnInterestUserCancel({
          userId: imCustomUserId.value,
        })
      } else {
        await unInterestPositionCancel({
          positionId: customCardInfo.value.positionInfoId,
        })
      }
      await clearConversationSilentMode(getConversationInfo.value.conversationId)
      await loadConversations(true)
    }
    await updateBlacklistUsers(true)
    uni.hideLoading()
  },
})

function handleClickLeft() {
  uni.navigateBack()
}
function handleReport() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams(`/sub_business/pages/conversation/report`, {
      conversationId: getConversationInfo.value.conversationId,
    }),
  })
}
</script>

<style lang="scss" scoped>
:deep(.wd-switch) {
  .wd-switch__circle {
    width: 46rpx;
    height: 32rpx;
    background: #bcbcbc;
    border-radius: 20rpx;
    &::after {
      display: none;
    }
  }
  &.is-checked {
    .wd-switch__circle {
      background: #3e9cff;
    }
  }
}
</style>
