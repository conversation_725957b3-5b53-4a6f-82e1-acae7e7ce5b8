<template>
  <!-- 背景遮罩 -->
  <view
    v-if="menuDisplayed"
    class="fixed inset-0 z-8"
    :class="[isMenuVisible ? 'animate-fade-in' : 'animate-fade-out']"
    @tap="hideMenu"
    @animationend="handleOverlayAnimationEnd"
  />

  <!-- 主容器 -->
  <view
    class="fixed z-20 header-r main-container"
    :class="{
      'transition-all duration-200 ease-out': !isDragging,
      'drag-shadow': isDragging,
    }"
    :style="{
      bottom: currentBottom + 'rpx',
      transform: isDragging ? 'scale(1.05)' : 'scale(1)',
    }"
  >
    <!-- 头像按钮 -->
    <view class="avatar-container">
      <view
        @tap="handleIconTap"
        class="avatar-bg select-none"
        :class="{ 'drag-active': isDragging }"
        @touchstart="handleIconTouchStart"
        @touchmove.prevent="handleIconTouchMove"
        @touchend="handleIconTouchEnd"
      >
        <image class="avatar-image" :src="avatarUrl" mode="aspectFill" />
      </view>

      <!-- 功能按钮组 - 围绕头像环绕 -->
      <view
        v-if="menuDisplayed"
        class="function-buttons-container"
        :class="[isMenuVisible ? 'animate-buttons-in' : 'animate-buttons-out']"
      >
        <view
          v-for="(item, index) in menuItems"
          :key="index"
          class="function-button"
          :class="`button-position-${index}`"
          :style="{
            animationDelay: isMenuVisible ? `${index * 0.1}s` : '0s',
          }"
          @tap="handleAction(item.action)"
        >
          <view class="button-content">
            <wd-img :width="20" :height="20" :src="`/static/img/home/<USER>" />
            <text class="button-text">{{ item.label }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
// 头像图片 - 使用项目中的头像
import avatarUrl from '@/static/img/host.png'

// 获取参数
const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
})
console.log(props.params, 'props.params==============')

interface MenuItem {
  label: string
  action: string
}
const emit = defineEmits(['handleSentResumes'])
const toast = useToast()

const { bool: isMenuVisible, setTrue: showMenu, setFalse: hideMenu } = useBoolean()
const { bool: menuDisplayed, setTrue: showMenuDOM, setFalse: hideMenuDOM } = useBoolean()
// 菜单项 - 根据图片中的功能
const menuItems: MenuItem[] = [
  { label: '投', action: 'submit_resume' },
  { label: '搜', action: 'search_jobs' },

  { label: 'AI', action: 'ai_assistant' },
  { label: '面', action: 'interview' },
]

// 从本地存储获取上次保存的位置，如果没有则使用默认位置400
const getSavedPosition = () => {
  try {
    const saved = uni.getStorageSync('god_horse_position')
    return saved ? Number(saved) : 400
  } catch (error) {
    console.warn('获取保存的位置失败:', error)
    return 400
  }
}

// 保存当前位置到本地存储
const savePosition = (position: number) => {
  try {
    uni.setStorageSync('god_horse_position', position.toString())
  } catch (error) {
    console.warn('保存位置失败:', error)
  }
}

const currentBottom = ref(getSavedPosition()) // 初始位置从本地存储读取
const isDragging = ref(false)
const startY = ref(0)
const startBottom = ref(0)
const moveThreshold = 5 // 适中的阈值，防止误触但保持敏感度
const hasMoved = ref(false)

let cachedBoundaries: { min: number; max: number } | null = null
let cachedPixelRatio: number | null = null

const getPixelRatio = () => {
  if (!cachedPixelRatio) {
    const systemInfo = uni.getSystemInfoSync()
    cachedPixelRatio = 750 / systemInfo.screenWidth
  }
  return cachedPixelRatio
}

const getBoundaries = () => {
  if (!cachedBoundaries) {
    const systemInfo = uni.getSystemInfoSync()
    const screenHeight = systemInfo.screenHeight * (750 / systemInfo.screenWidth)
    cachedBoundaries = {
      min: 100, // 允许图标移动到更下方的位置
      max: screenHeight - 200, // 确保图标不会移动到屏幕顶部太高的位置
    }
  }
  return cachedBoundaries
}
const toggleMenu = () => {
  if (!isMenuVisible.value) {
    showMenu()
    showMenuDOM()
  } else {
    hideMenu()
  }
}

const handleOverlayAnimationEnd = () => {
  if (!isMenuVisible.value) {
    hideMenuDOM()
  }
}

const handleAction = (action: string) => {
  const actionHandlers: Record<string, () => void> = {
    submit_resume: () => {
      // 这里可以添加投递简历的逻辑
      emit('handleSentResumes')
    },
    search_jobs: () => {
      // 这里可以添加搜索岗位的逻辑
      uni.navigateTo({
        url:
          '/resumeRelated/HomeSearch/index?params=' +
          encodeURIComponent(JSON.stringify(props.params)),
      })
    },
    ai_assistant: () => {
      // 这里可以添加AI助手的逻辑
      uni.switchTab({
        url: '/pages/deepseek/index',
      })
    },
    interview: () => {
      uni.navigateTo({
        url: '/resumeRelated/interview/index',
      })

      // 这里可以添加面试相关的逻辑
    },
  }

  const handler = actionHandlers[action] || (() => console.log('未知操作类型:', action))
  handler()
  hideMenu()
}

const handleIconTouchStart = (e: TouchEvent) => {
  // 使用 clientY 简化坐标处理
  startY.value = e.touches[0].clientY
  startBottom.value = currentBottom.value
  hasMoved.value = false
  isDragging.value = false
}

const handleIconTouchMove = (e: TouchEvent) => {
  // 简化坐标处理，使用 clientY
  const currentY = e.touches[0].clientY
  const deltaY = currentY - startY.value

  if (!isDragging.value) {
    // 简化阈值判断
    if (Math.abs(deltaY) > moveThreshold) {
      isDragging.value = true
      hasMoved.value = true
    } else {
      return
    }
  }

  // 计算新位置，需要将像素转换为 rpx
  const pixelRatio = getPixelRatio()
  const deltaYRpx = deltaY * pixelRatio
  const boundaries = getBoundaries()
  const newBottom = startBottom.value - deltaYRpx
  const clampedBottom = Math.max(boundaries.min, Math.min(boundaries.max, newBottom))
  currentBottom.value = clampedBottom
}

const handleIconTouchEnd = () => {
  if (isDragging.value) {
    isDragging.value = false
    const boundaries = getBoundaries()
    const current = currentBottom.value
    const snapDistance = 60 // 增加吸附距离，使用 rpx 单位

    // 边界吸附逻辑
    if (current - boundaries.min < snapDistance) {
      currentBottom.value = boundaries.min
    } else if (boundaries.max - current < snapDistance) {
      currentBottom.value = boundaries.max
    }

    // 保存最终位置到本地存储
    savePosition(currentBottom.value)
  }
}

const handleIconTap = () => {
  if (!hasMoved.value) {
    toggleMenu()
  }
  nextTick(() => {
    hasMoved.value = false
  })
}
// 组件卸载时的清理工作
onUnmounted(() => {
  // 清理工作
})
</script>

<style lang="scss" scoped>
.header-r {
  right: -100rpx;
}
/* 主容器样式 */
.main-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
/* 头像容器样式 */
.avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 300rpx;
}

.avatar-bg {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 130rpx;
  height: 130rpx;
  margin-right: -60rpx;
  // background: rgba(0, 0, 0, 0.8);
  // border-radius: 50%;
  // box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.avatar-image {
  position: absolute;
  top: 10rpx;
  right: 45rpx;
  z-index: 1001;
  width: 140rpx;
  height: 140rpx;
}
/* 功能按钮容器 */
.function-buttons-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
/* 功能按钮样式 */
.function-button {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  // width: 130rpx;
  height: 56rpx;
  padding: 0 20rpx;
  pointer-events: auto;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: all 0.3s ease;

  // &:active {
  //   background: rgba(0, 0, 0, 0.9);
  //   transform: scale(0.9);
  // }
}
/* 按钮位置定位 - 围绕头像环绕 */
.button-position-0 {
  /* 投 - 左上角 */
  top: 0rpx;
  left: 60rpx;
}

.button-position-1 {
  /* 搜 - 右上角 */
  top: 80rpx;
  left: -20rpx;
}

.button-position-2 {
  /* AI - 左下角 */
  top: 180rpx;
  left: -20rpx;
}

.button-position-3 {
  /* 面 - 右下角 */
  bottom: -20rpx;
  left: 60rpx;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text {
  padding-left: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}
/* 按钮动画 */
@keyframes buttons-in {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes buttons-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.3);
  }
}

.animate-buttons-in .function-button {
  animation: buttons-in 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-buttons-out .function-button {
  animation: buttons-out 0.3s ease-in forwards;
}
/* 确保按钮在动画开始前是隐藏的 */
.function-buttons-container .function-button {
  opacity: 0;
  transform: scale(0.3);
}
/* 背景遮罩动画 */
@keyframes fade-in-overlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out-overlay {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fade-in-overlay 0.2s ease-out forwards;
}

.animate-fade-out {
  animation: fade-out-overlay 0.2s ease-in forwards;
}
/* 拖拽相关样式 */
.drag-shadow {
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.15));
  will-change: transform, filter;
}

.drag-active {
  opacity: 0.9;
  will-change: opacity;
}

.select-none {
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.transition-all {
  will-change: transform, bottom;
}
</style>
