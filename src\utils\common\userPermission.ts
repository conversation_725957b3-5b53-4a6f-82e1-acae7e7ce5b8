/**
 * 登录 || 未登录执行
 */
export const userLoginStateExecute = (login: () => void, noLogin: () => void) => {
  const { getUserIsLogin } = useUserInfo()
  if (getUserIsLogin.value) {
    login()
  } else {
    noLogin()
  }
}
/**
 * 用户登录了再执行
 */
export const userLoginExecute = <T extends (...args: any[]) => any>(fn: T) => {
  const loginExecute = (...args: Parameters<T>) => {
    userLoginStateExecute(
      () => {
        fn(...args)
      },
      () => {
        uni.reLaunch({
          url: '/pages/login/index',
        })
      },
    )
  }
  return loginExecute
}
