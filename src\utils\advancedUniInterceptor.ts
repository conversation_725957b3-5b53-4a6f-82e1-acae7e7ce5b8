import { permissionManager } from './permission'
import { PermissionType } from '@/types/permission'

/**
 * 高级uni API拦截器
 */
export function setupAdvancedUniInterceptor() {
  // 拦截chooseImage
  const originalChooseImage = uni.chooseImage
  uni.chooseImage = async function (options: UniApp.ChooseImageOptions = {}) {
    console.log('拦截chooseImage调用', options)

    const sourceType = options.sourceType || ['album', 'camera']
    const availableSourceType: ('album' | 'camera')[] = []

    try {
      // 检查相机权限
      if (sourceType.includes('camera')) {
        const cameraResult = await permissionManager.checkPermission('camera')
        if (cameraResult.granted) {
          availableSourceType.push('camera')
          console.log('相机权限可用')
        } else {
          console.log('相机权限未授予，从选项中移除')
        }
      }

      // 检查相册权限
      if (sourceType.includes('album')) {
        const albumResult = await permissionManager.checkPermission('photoLibrary')
        if (albumResult.granted) {
          availableSourceType.push('album')
          console.log('相册权限可用')
        } else {
          console.log('相册权限未授予，从选项中移除')
        }
      }

      // 如果没有任何可用的权限，才返回失败
      if (availableSourceType.length === 0) {
        console.log('没有可用的图片来源权限')
        uni.showToast({
          title: '需要相机或相册权限',
          icon: 'none',
        })
        if (options.fail) {
          options.fail({
            errMsg: 'chooseImage:fail 没有可用的权限',
          })
        }
        return
      }

      console.log('chooseImage 可用权限检查通过，sourceType:', availableSourceType)
      return originalChooseImage.call(this, options)
    } catch (error) {
      console.error('权限检查出错:', error)
      if (options.fail) {
        options.fail({
          errMsg: 'chooseImage:fail 权限检查失败',
        })
      }
    }
  }

  // 拦截chooseVideo
  const originalChooseVideo = uni.chooseVideo
  uni.chooseVideo = async function (options: UniApp.ChooseVideoOptions = {}) {
    console.log('拦截chooseVideo调用', options)

    const sourceType = options.sourceType || ['album', 'camera']
    const availableSourceType: ('album' | 'camera')[] = []

    try {
      // 检查相机权限
      if (sourceType.includes('camera')) {
        const cameraResult = await permissionManager.checkPermission('camera')
        if (cameraResult.granted) {
          availableSourceType.push('camera')
        }
      }

      // 检查相册权限
      if (sourceType.includes('album')) {
        const albumResult = await permissionManager.checkPermission('photoLibrary')
        if (albumResult.granted) {
          availableSourceType.push('album')
        }
      }

      // 如果没有任何可用的权限
      if (availableSourceType.length === 0) {
        uni.showToast({
          title: '需要相机或相册权限',
          icon: 'none',
        })
        if (options.fail) {
          options.fail({
            errMsg: 'chooseVideo:fail 没有可用的权限',
          })
        }
        return
      }

      return originalChooseVideo.call(this, options)
    } catch (error) {
      console.error('权限检查出错:', error)
      if (options.fail) {
        options.fail({
          errMsg: 'chooseVideo:fail 权限检查失败',
        })
      }
    }
  }

  // 拦截startRecord
  //   const originalStartRecord = uni.startRecord
  //   uni.startRecord = async function (options: UniApp.StartRecordOptions = {}) {
  //     console.log('拦截startRecord调用')

  //     try {
  //       const result = await permissionManager.checkPermission('record')
  //       if (!result.granted) {
  //         uni.showToast({
  //           title: '需要录音权限',
  //           icon: 'none',
  //         })
  //         if (options.fail) {
  //           options.fail({
  //             errMsg: 'startRecord:fail 录音权限未授予',
  //           })
  //         }
  //         return
  //       }

  //       return originalStartRecord.call(this, options)
  //     } catch (error) {
  //       console.error('录音权限检查出错:', error)
  //       if (options.fail) {
  //         options.fail({
  //           errMsg: 'startRecord:fail 权限检查失败',
  //         })
  //       }
  //     }
  //   }

  // 拦截getLocation
  //   const originalGetLocation = uni.getLocation
  //   uni.getLocation = async function (options: UniApp.GetLocationOptions = {}) {
  //     console.log('拦截getLocation调用')
  //     try {
  //       const result = await permissionManager.checkPermission('location')
  //       if (!result.granted) {
  //         uni.showToast({
  //           title: '需要位置权限',
  //           icon: 'none',
  //         })
  //         if (options.fail) {
  //           options.fail({
  //             errMsg: 'getLocation:fail 位置权限未授予',
  //           })
  //         }
  //         return
  //       }

  //       return originalGetLocation.call(this, options)
  //     } catch (error) {
  //       console.error('位置权限检查出错:', error)
  //       if (options.fail) {
  //         options.fail({
  //           errMsg: 'getLocation:fail 权限检查失败',
  //         })
  //       }
  //     }
  //   }
}
