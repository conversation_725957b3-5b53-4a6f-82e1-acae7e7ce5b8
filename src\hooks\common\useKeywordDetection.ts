import { KEYWORDS_DETECTION, USER_TYPE } from '@/enum'

/** 关键词检测 */
export const useKeywordDetection = () => {
  const { userIntel } = useUserInfo()
  const cache = new Map<string, ReturnType<typeof preprocessText>>()
  const kmpTableCache = new Map<string, number[]>()

  const HOMOPHONE_MAP = new Map([
    ['0', 'o零〇洞'],
    ['1', 'i一壹l丨I'],
    ['2', 'z二贰两俩Z'],
    ['3', '三叁仨'],
    ['4', '四肆'],
    ['5', '五伍wu'],
    ['6', '六陆liu'],
    ['7', '七柒qi'],
    ['8', '八捌ba'],
    ['9', '九玖jiu'],
    ['a', '@艾爱A'],
    ['o', '0零〇洞O'],
    ['i', '1一壹l丨I'],
    ['l', '1一壹i丨L'],
    ['s', '$S'],
    ['z', '2二贰Z'],
    ['x', '×乘X'],
    ['q', 'Q'],
  ])

  const NOISE_PATTERNS = [
    /\s|\u200B|\u200C|\u200D|\uFEFF/g,
    /[.。,，;；:：!！?？]/g,
    /[-_=+*#@&%]/g,
    /[0-9]/g,
  ]

  /** 匹配类型枚举 */
  const MATCH_TYPES = {
    EXACT: { type: 'exact', confidence: 1.0 },
    NORMALIZED: { type: 'normalized', confidence: 0.95 },
    HOMOPHONE: { type: 'homophone', confidence: 0.9 },
    SEQUENCE: { type: 'sequence', confidence: 0.85 },
    FUZZY: { type: 'fuzzy', baseConfidence: 0.8 },
    COMBINATION: { type: 'combination', multiplier: 0.9 },
  } as const

  /** 常用词白名单 - 需要更严格的上下文检查 */
  const COMMON_WORDS_WHITELIST = {
    // 支付相关常用词
    PAYMENT: ['钱', '付款', '收费', '费用', '支付', '现金', '银行卡', '工资', '薪水', '收入'],
    // 性别相关常用词
    GENDER: ['男', '女', '男性', '女性', '先生', '女士'],
    // 地域相关常用词
    LOCATION: ['河南', '东北', '安徽', '外地', '本地', '当地', '附近'],
    // 年龄相关常用词
    AGE: ['年龄', '岁', '多大', '几岁'],
    // 其他常用词
    COMMON: ['免费', '保证', '一定', '最好', '最优', '优秀', '专业', '经验'],
  }

  /** 高风险上下文模式 - 这些词出现时才算违规 */
  const HIGH_RISK_CONTEXT_PATTERNS = {
    钱: [
      /先交.*钱/,
      /预付.*钱/,
      /保证金.*钱/,
      /培训.*钱/,
      /报名.*钱/,
      /交.*钱.*入职/,
      /钱.*才能.*工作/,
      /需要.*钱.*报名/,
      /收.*钱.*培训/,
      /.*钱.*办理/,
      /缴.*钱/,
      /押.*钱/,
      /费.*钱/,
      /钱.*手续/,
    ],
    付款: [
      /入职.*付款/,
      /先.*付款/,
      /工作.*付款/,
      /岗位.*付款/,
      /培训.*付款/,
      /报名.*付款/,
      /办理.*付款/,
      /需要.*付款/,
      /必须.*付款/,
    ],
    收费: [
      /入职.*收费/,
      /工作.*收费/,
      /岗位.*收费/,
      /培训.*收费/,
      /报名.*收费/,
      /办理.*收费/,
      /需要.*收费/,
      /必须.*收费/,
    ],
    费用: [
      /入职.*费用/,
      /工作.*费用/,
      /培训.*费用/,
      /报名.*费用/,
      /办理.*费用/,
      /需要.*费用/,
      /必须.*费用/,
      /先交.*费用/,
      /预付.*费用/,
    ],
    支付: [
      /入职.*支付/,
      /工作.*支付/,
      /培训.*支付/,
      /报名.*支付/,
      /办理.*支付/,
      /需要.*支付/,
      /必须.*支付/,
      /先.*支付/,
      /预.*支付/,
    ],
    男: [
      /限.*男/,
      /只要.*男/,
      /必须.*男/,
      /仅限.*男/,
      /只招.*男/,
      /专招.*男/,
      /不要.*女/,
      /拒绝.*女/,
      /男生优先/,
      /男性优先/,
      /男.*优先/,
    ],
    女: [
      /限.*女/,
      /只要.*女/,
      /必须.*女/,
      /仅限.*女/,
      /只招.*女/,
      /专招.*女/,
      /不要.*男/,
      /拒绝.*男/,
      /女生优先/,
      /女性优先/,
      /女.*优先/,
    ],
    年龄: [
      /年龄.*限制/,
      /.*岁.*以下/,
      /.*岁.*以上/,
      /年龄.*要求/,
      /超过.*岁.*不要/,
      /.*岁.*不考虑/,
      /年龄.*不符/,
      /年龄.*标准/,
    ],
    河南: [
      /不要.*河南/,
      /拒绝.*河南/,
      /河南.*不要/,
      /河南.*不行/,
      /不招.*河南/,
      /河南.*人.*不/,
      /除了.*河南/,
      /河南.*的.*别/,
    ],
    东北: [
      /不要.*东北/,
      /拒绝.*东北/,
      /东北.*不要/,
      /东北.*不行/,
      /不招.*东北/,
      /东北.*人.*不/,
      /除了.*东北/,
      /东北.*的.*别/,
    ],
    安徽: [
      /不要.*安徽/,
      /拒绝.*安徽/,
      /安徽.*不要/,
      /安徽.*不行/,
      /不招.*安徽/,
      /安徽.*人.*不/,
      /除了.*安徽/,
      /安徽.*的.*别/,
    ],
    免费: [
      /完全.*免费/,
      /绝对.*免费/,
      /百分百.*免费/,
      /全程.*免费/,
      /永久.*免费/,
      /免费.*培训.*就业/,
      /免费.*包.*工作/,
      /免费.*包.*分配/,
    ],
    保证: [
      /保证.*赚钱/,
      /保证.*月入/,
      /保证.*收入/,
      /保证.*工作/,
      /保证.*录取/,
      /保证.*通过/,
      /保证.*成功/,
      /百分百.*保证/,
      /绝对.*保证/,
    ],
    最好: [/史上.*最好/, /全网.*最好/, /业内.*最好/, /绝对.*最好/, /肯定.*最好/],
  }

  /** 安全上下文模式 - 这些词在此上下文中是安全的 */
  const SAFE_CONTEXT_PATTERNS = {
    钱: [
      /赚钱/,
      /挣钱/,
      /工资.*钱/,
      /薪水.*钱/,
      /收入.*钱/,
      /奖金.*钱/,
      /钱.*多少/,
      /多少.*钱/,
      /钱.*怎么样/,
      /待遇.*钱/,
      /钱.*方面/,
    ],
    付款: [
      /客户.*付款/,
      /公司.*付款/,
      /及时.*付款/,
      /按时.*付款/,
      /付款.*及时/,
      /付款.*周期/,
      /付款.*方式/,
      /工资.*付款/,
    ],
    男: [
      /男.*工程师/,
      /男.*设计师/,
      /男.*经理/,
      /男.*主管/,
      /男.*同事/,
      /男.*朋友/,
      /男.*员工/,
      /招聘.*男女不限/,
      /性别.*不限/,
    ],
    女: [
      /女.*工程师/,
      /女.*设计师/,
      /女.*经理/,
      /女.*主管/,
      /女.*同事/,
      /女.*朋友/,
      /女.*员工/,
      /招聘.*男女不限/,
      /性别.*不限/,
    ],
    免费: [
      /免费.*班车/,
      /免费.*食堂/,
      /免费.*宿舍/,
      /免费.*体检/,
      /免费.*停车/,
      /免费.*午餐/,
      /免费.*晚餐/,
      /免费.*wifi/,
      /免费.*健身/,
      /福利.*免费/,
    ],
  }

  /** 合法上下文模式 */
  const LEGITIMATE_CONTEXT_PATTERNS = {
    微信: [
      /我们?可以加个?微信/,
      /交换一?下微信/,
      /微信联系/,
      /有微信吗/,
      /微信号/,
      /发个?微信/,
      /微信聊/,
      /微信方便/,
      /留个?微信/,
      /通过微信/,
      /微信沟通/,
      /微信详聊/,
      /微信交流/,
      /微信讨论/,
      /方便微信/,
      /微信谈/,
    ],
    qq: [
      /我们?可以加个?qq/,
      /交换一?下qq/,
      /qq联系/,
      /有qq吗/,
      /qq号/,
      /发个?qq/,
      /qq聊/,
      /通过qq/,
      /qq沟通/,
      /qq详聊/,
      /qq交流/,
      /方便qq/,
    ],
    手机: [
      /手机方便/,
      /手机联系/,
      /留个?手机号/,
      /手机号码/,
      /交换一?下手机号/,
      /我们?可以交换手机号/,
      /能交换个?手机号/,
      /交换个?联系方式/,
      /互换手机号/,
      /换个?手机号/,
      /有手机号吗/,
      /手机号方便/,
      /通过手机/,
      /手机沟通/,
      /手机详聊/,
      /手机谈/,
      /发个?手机/,
    ],
    手机号: [
      /交换一?下手机号/,
      /我们?可以交换手机号/,
      /能交换个?手机号/,
      /互换手机号/,
      /换个?手机号/,
      /有手机号吗/,
      /手机号方便/,
      /留个?手机号/,
      /发个?手机号/,
      /手机号联系/,
      /手机号沟通/,
      /手机号详聊/,
      /给个?手机号/,
    ],
    联系方式: [
      /交换个?联系方式/,
      /我们?可以交换联系方式/,
      /留个?联系方式/,
      /互换联系方式/,
      /有联系方式吗/,
      /联系方式方便/,
      /其他联系方式/,
      /给个?联系方式/,
      /发个?联系方式/,
      /联系方式详聊/,
      /便于联系方式/,
      /方便联系方式/,
    ],
    电话: [
      /电话联系/,
      /电话沟通/,
      /电话详聊/,
      /电话方便/,
      /打个?电话/,
      /电话谈/,
      /通过电话/,
      /电话交流/,
      /电话号码/,
      /有电话吗/,
      /留个?电话/,
      /发个?电话/,
    ],
    私聊: [
      /我们?私聊/,
      /可以私聊/,
      /私聊详聊/,
      /私聊一下/,
      /私聊沟通/,
      /私聊谈/,
      /私聊交流/,
      /方便私聊/,
      /私聊讨论/,
    ],
    加好友: [/可以加个?好友/, /我们?加个?好友/, /互加好友/, /加好友聊/, /加好友详聊/, /加好友交流/],
    联系: [
      /联系详聊/,
      /联系沟通/,
      /联系交流/,
      /联系谈/,
      /方便联系/,
      /保持联系/,
      /随时联系/,
      /联系讨论/,
    ],
    沟通: [
      /深入沟通/,
      /详细沟通/,
      /进一步沟通/,
      /线下沟通/,
      /当面沟通/,
      /电话沟通/,
      /视频沟通/,
      /面对面沟通/,
    ],
    详聊: [/我们?详聊/, /可以详聊/, /方便详聊/, /线下详聊/, /电话详聊/, /微信详聊/, /qq详聊/],
  }

  /** 检查是否为有效的反向匹配 */
  const isValidReverseMatch = (content: string, keyword: string): boolean => {
    const lengthRatio = keyword.length / content.length

    // 长度比例过大（关键词比内容长很多），认为是误匹配
    if (lengthRatio >= 2.5) {
      console.log(
        `🚫 反向匹配过滤: 长度比例过大 "${content}"(${content.length}) vs "${keyword}"(${keyword.length}), 比例: ${lengthRatio}`,
      )
      return false
    }

    // 如果内容长度 <= 2 且是关键词的一部分，需要更严格的检查
    if (content.length <= 2) {
      // 检查是否只是关键词的前缀或后缀
      const isPrefix = keyword.startsWith(content)
      const isSuffix = keyword.endsWith(content)

      if (isPrefix || isSuffix) {
        // 如果内容只有1个字符，直接过滤
        if (content.length === 1) {
          console.log(
            `🚫 反向匹配过滤: 单字符"${content}"是关键词"${keyword}"的${isPrefix ? '前缀' : '后缀'}`,
          )
          return false
        }

        // 如果是2个字符的前缀/后缀，且关键词长度 >= 4，也过滤
        if (content.length === 2 && keyword.length >= 4) {
          console.log(
            `🚫 反向匹配过滤: 短词"${content}"只是长关键词"${keyword}"的${isPrefix ? '前缀' : '后缀'}`,
          )
          return false
        }
      }
    }

    return true
  }

  /** 检查是否为常用词且在安全上下文中 */
  const isCommonWordInSafeContext = (content: string, keyword: string): boolean => {
    // 检查是否为常用词
    const isCommonWord = Object.values(COMMON_WORDS_WHITELIST).flat().includes(keyword)
    if (!isCommonWord) return false

    // 检查是否在安全上下文中
    const safePatterns = SAFE_CONTEXT_PATTERNS[keyword]
    if (safePatterns && safePatterns.some((pattern) => pattern.test(content))) {
      return true
    }

    // 检查是否在高风险上下文中
    const riskPatterns = HIGH_RISK_CONTEXT_PATTERNS[keyword]
    if (riskPatterns) {
      const hasRiskContext = riskPatterns.some((pattern) => pattern.test(content))
      return !hasRiskContext // 如果没有高风险上下文，则认为是安全的
    }

    return true // 如果是常用词但没有定义风险模式，默认认为安全
  }

  /** 检查是否为合法上下文使用 */
  const isLegitimateContext = (content: string, keyword: string): boolean => {
    const patterns = LEGITIMATE_CONTEXT_PATTERNS[keyword.toLowerCase()]
    if (!patterns) return false
    return patterns.some((pattern) => pattern.test(content))
  }

  /** 增强的短词过滤规则 */
  const shouldSkipShortWord = (
    keyword: string,
    content: string,
    confidence: number,
    matchType?: string,
  ): boolean => {
    // 单字符直接跳过
    if (keyword.length === 1) {
      console.log(`⚠️ 跳过单字符: "${keyword}"`)
      return true
    }

    // 对于反向匹配的特殊处理
    if (matchType?.includes('partial_')) {
      const lengthRatio = content.length / keyword.length

      // 如果内容长度是关键词长度的很小一部分，且置信度不够高
      if (lengthRatio <= 0.4 && confidence < 0.98) {
        console.log(
          `⚠️ 跳过反向匹配短词: "${keyword}", 长度比例: ${lengthRatio.toFixed(2)}, 置信度: ${confidence}`,
        )
        return true
      }

      // 对于1-2字符的内容匹配长关键词，更严格
      if (content.length <= 2 && keyword.length >= 3) {
        console.log(
          `⚠️ 跳过反向匹配: 短内容"${content}"(${content.length})匹配长关键词"${keyword}"(${keyword.length})`,
        )
        return true
      }
    }

    // 2字符以下的处理
    if (keyword.length <= 2) {
      // 检查常用词安全上下文
      if (isCommonWordInSafeContext(content, keyword)) {
        console.log(`⚠️ 跳过常用词: "${keyword}", 原因: 安全上下文使用`)
        return true
      }

      // 检查合法上下文
      if (isLegitimateContext(content, keyword)) {
        console.log(`⚠️ 跳过短词: "${keyword}", 原因: 合法上下文`)
        return true
      }

      // 根据匹配类型调整置信度要求
      const minConfidence = matchType?.includes('partial_') ? 0.98 : 0.95
      if (confidence < minConfidence) {
        console.log(
          `⚠️ 跳过短词: "${keyword}", 原因: 置信度不足 (${confidence} < ${minConfidence})`,
        )
        return true
      }
    }

    // 3字符的处理
    if (keyword.length === 3) {
      if (isCommonWordInSafeContext(content, keyword)) {
        console.log(`⚠️ 跳过常用词: "${keyword}", 原因: 安全上下文使用`)
        return true
      }

      if (isLegitimateContext(content, keyword)) {
        console.log(`⚠️ 跳过短词: "${keyword}", 原因: 合法上下文`)
        return true
      }

      const minConfidence = matchType?.includes('partial_') ? 0.95 : 0.9
      if (confidence < minConfidence) {
        console.log(
          `⚠️ 跳过短词: "${keyword}", 原因: 置信度不足 (${confidence} < ${minConfidence})`,
        )
        return true
      }
    }

    // 4-5字符的常用词也需要更高置信度
    if (keyword.length <= 5 && COMMON_WORDS_WHITELIST.COMMON.includes(keyword)) {
      if (isCommonWordInSafeContext(content, keyword)) {
        console.log(`⚠️ 跳过常用词: "${keyword}", 原因: 安全上下文使用`)
        return true
      }

      const minConfidence = matchType?.includes('partial_') ? 0.92 : 0.85
      if (confidence < minConfidence) {
        console.log(
          `⚠️ 跳过常用词: "${keyword}", 原因: 置信度不足 (${confidence} < ${minConfidence})`,
        )
        return true
      }
    }

    return false
  }

  const preprocessText = (text: string) => {
    if (!text) {
      return {
        original: '',
        normalized: '',
        homophone: '',
        chinese: '',
        alphanumeric: '',
      }
    }
    if (cache.has(text)) {
      return cache.get(text)!
    }
    const original = text.toLowerCase()
    let normalized = original
    normalized = NOISE_PATTERNS.reduce((acc, pattern) => acc.replace(pattern, ''), normalized)

    let homophone = normalized
    for (const [target, sources] of Array.from(HOMOPHONE_MAP.entries())) {
      const sourceChars = sources.split('')
      for (const char of sourceChars) {
        if (char.length === 1 && normalized.includes(char)) {
          const regex = new RegExp(char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
          homophone = homophone.replace(regex, target)
        }
      }
    }

    const result = {
      original,
      normalized,
      homophone,
      chinese: normalized.replace(/[^a-z\u4e00-\u9fa5]/g, ''),
      alphanumeric: normalized.replace(/[^\w]/g, ''),
    }
    cache.set(text, result)
    return result
  }

  /** KMP算法 */
  const buildKMPTable = (pattern: string): number[] => {
    if (kmpTableCache.has(pattern)) {
      return kmpTableCache.get(pattern)!
    }

    const table = new Array(pattern.length).fill(0)
    let j = 0

    for (let i = 1; i < pattern.length; i++) {
      while (j > 0 && pattern[i] !== pattern[j]) {
        j = table[j - 1]
      }
      if (pattern[i] === pattern[j]) {
        j++
      }
      table[i] = j
    }

    kmpTableCache.set(pattern, table)
    return table
  }

  const kmpSearch = (text: string, pattern: string): boolean => {
    if (!pattern || !text || pattern.length > text.length) return false

    const table = buildKMPTable(pattern)
    let j = 0

    for (let i = 0; i < text.length; i++) {
      while (j > 0 && text[i] !== pattern[j]) {
        j = table[j - 1]
      }
      if (text[i] === pattern[j]) {
        j++
      }
      if (j === pattern.length) {
        return true
      }
    }
    return false
  }

  /** 序列匹配算法 */
  const sequenceMatch = (text: string, pattern: string, maxGap: number = 3): boolean => {
    if (!text || !pattern || pattern.length < 2 || text.length < pattern.length) return false

    let patternIndex = 0
    let lastMatchPos = -1

    for (let i = 0; i < text.length && patternIndex < pattern.length; i++) {
      if (text[i] === pattern[patternIndex]) {
        if (lastMatchPos >= 0 && i - lastMatchPos - 1 > maxGap) {
          patternIndex = text[i] === pattern[0] ? 1 : 0
        } else {
          patternIndex++
        }
        lastMatchPos = i
      }
    }

    return patternIndex === pattern.length
  }

  /** 编辑距离算法 */
  const levenshteinDistance = (s1: string, s2: string, maxDistance?: number): number => {
    if (s1.length === 0) return s2.length
    if (s2.length === 0) return s1.length

    if (maxDistance && Math.abs(s1.length - s2.length) > maxDistance) {
      return maxDistance + 1
    }

    let prev = Array(s2.length + 1)
      .fill(0)
      .map((_, i) => i)
    let curr = Array(s2.length + 1).fill(0)

    for (let i = 1; i <= s1.length; i++) {
      curr[0] = i
      let minInRow = i

      for (let j = 1; j <= s2.length; j++) {
        if (s1[i - 1] === s2[j - 1]) {
          curr[j] = prev[j - 1]
        } else {
          curr[j] = Math.min(prev[j] + 1, curr[j - 1] + 1, prev[j - 1] + 1)
        }
        minInRow = Math.min(minInRow, curr[j])
      }
      if (maxDistance && minInRow > maxDistance) {
        return maxDistance + 1
      }

      ;[prev, curr] = [curr, prev]
    }

    return prev[s2.length]
  }

  const splitWordMatch = (
    text: string,
    keywords: string[],
  ): { matched: boolean; keyword: string; confidence: number } => {
    if (!text || !keywords?.length) return { matched: false, keyword: '', confidence: 0 }

    const textChars = text.replace(/\s+/g, '').split('')

    for (const keyword of keywords) {
      const keywordChars = keyword.split('')

      let matchedChars = 0
      let lastIndex = -1

      for (const char of keywordChars) {
        const charIndex = textChars.indexOf(char, lastIndex + 1)
        if (charIndex > lastIndex) {
          matchedChars++
          lastIndex = charIndex
        }
      }

      const matchRatio = matchedChars / keywordChars.length
      if (matchRatio >= 0.8 && matchedChars >= 3) {
        return {
          matched: true,
          keyword,
          confidence: matchRatio * 0.9,
        }
      }
    }

    return { matched: false, keyword: '', confidence: 0 }
  }

  /** 词汇拆分匹配 */
  const wordDecompositionMatch = (
    content: ReturnType<typeof preprocessText>,
    keywords: string[],
  ): { matched: boolean; keyword: string; confidence: number } => {
    if (!keywords || keywords.length === 0) {
      return { matched: false, keyword: '', confidence: 0 }
    }
    const validKeywords = keywords.filter((keyword) => keyword && keyword.length >= 2)
    return splitWordMatch(content.normalized, validKeywords)
  }

  const isMatch = (
    content: ReturnType<typeof preprocessText>,
    keyword: string,
  ): { matched: boolean; confidence: number; type: string } => {
    if (!keyword?.trim()) {
      return { matched: false, confidence: 0, type: 'none' }
    }

    const processedKeyword = preprocessText(keyword)

    if (!content.normalized && !content.original) {
      return { matched: false, confidence: 0, type: 'none' }
    }

    // 直接包含检查
    if (
      content.original.includes(keyword) ||
      content.normalized.includes(processedKeyword.normalized)
    ) {
      return { matched: true, confidence: 1.0, type: 'direct_include' }
    }

    // 正向匹配策略
    const forwardStrategies = [
      {
        check: () => content.original && kmpSearch(content.original, processedKeyword.original),
        result: MATCH_TYPES.EXACT,
      },
      {
        check: () =>
          content.normalized && kmpSearch(content.normalized, processedKeyword.normalized),
        result: MATCH_TYPES.NORMALIZED,
      },
      {
        check: () => content.homophone && kmpSearch(content.homophone, processedKeyword.homophone),
        result: MATCH_TYPES.HOMOPHONE,
      },
      {
        check: () =>
          content.normalized && sequenceMatch(content.normalized, processedKeyword.normalized),
        result: MATCH_TYPES.SEQUENCE,
      },
    ]

    // 反向匹配策略 - 增加有效性检查
    const reverseStrategies = [
      {
        check: () => {
          const canMatch =
            processedKeyword.original && kmpSearch(processedKeyword.original, content.original)
          return canMatch && isValidReverseMatch(content.original, processedKeyword.original)
        },
        result: { ...MATCH_TYPES.EXACT, confidence: 0.9 },
      },
      {
        check: () => {
          const canMatch =
            processedKeyword.normalized &&
            kmpSearch(processedKeyword.normalized, content.normalized)
          return canMatch && isValidReverseMatch(content.normalized, processedKeyword.normalized)
        },
        result: { ...MATCH_TYPES.NORMALIZED, confidence: 0.85 },
      },
      {
        check: () => {
          const canMatch =
            processedKeyword.homophone && kmpSearch(processedKeyword.homophone, content.homophone)
          return canMatch && isValidReverseMatch(content.homophone, processedKeyword.homophone)
        },
        result: { ...MATCH_TYPES.HOMOPHONE, confidence: 0.8 },
      },
      {
        check: () => {
          const canMatch =
            processedKeyword.normalized &&
            sequenceMatch(processedKeyword.normalized, content.normalized)
          return canMatch && isValidReverseMatch(content.normalized, processedKeyword.normalized)
        },
        result: { ...MATCH_TYPES.SEQUENCE, confidence: 0.75 },
      },
    ]

    // 检查正向匹配
    for (const strategy of forwardStrategies) {
      if (strategy.check()) {
        return {
          matched: true,
          confidence: strategy.result.confidence,
          type: strategy.result.type,
        }
      }
    }

    // 检查反向匹配 - 已经包含有效性检查
    for (const strategy of reverseStrategies) {
      if (strategy.check()) {
        return {
          matched: true,
          confidence: strategy.result.confidence,
          type: `partial_${strategy.result.type}`,
        }
      }
    }

    // 模糊匹配 - 提高最小长度要求和精度
    if (processedKeyword.normalized.length >= 3 && content.normalized.length >= 2) {
      const maxLen = Math.max(processedKeyword.normalized.length, content.normalized.length)
      const minLen = Math.min(processedKeyword.normalized.length, content.normalized.length)

      // 长度比例检查 - 更严格
      if (maxLen / minLen > 2.5) {
        return { matched: false, confidence: 0, type: 'none' }
      }

      const maxDistance = Math.ceil(Math.min(maxLen * 0.3, minLen * 0.4))
      const distance = levenshteinDistance(
        content.normalized,
        processedKeyword.normalized,
        maxDistance,
      )

      if (distance <= maxDistance) {
        const similarity = 1 - distance / maxLen
        let threshold = 0.7 // 提高基础阈值

        if (processedKeyword.normalized.length <= 3) {
          threshold = 0.9
        } else if (processedKeyword.normalized.length <= 4) {
          threshold = 0.8
        }

        const commonChars = new Set(
          [...content.normalized].filter((char) => processedKeyword.normalized.includes(char)),
        ).size
        const charRatio =
          commonChars / Math.min(content.normalized.length, processedKeyword.normalized.length)

        if (similarity >= threshold && charRatio >= 0.4) {
          return {
            matched: true,
            confidence: similarity * MATCH_TYPES.FUZZY.baseConfidence * Math.max(charRatio, 0.6),
            type: MATCH_TYPES.FUZZY.type,
          }
        }
      }
    }

    return { matched: false, confidence: 0, type: 'none' }
  }

  /** 组合匹配检测 */
  const checkCombinationMatch = (
    content: ReturnType<typeof preprocessText>,
    keywords: string[],
  ): { matched: boolean; confidence: number; matchedKeywords: string[] } => {
    if (!keywords || keywords.length === 0) {
      return { matched: false, confidence: 0, matchedKeywords: [] }
    }

    const matches = keywords
      .filter((keyword) => keyword?.trim())
      .map((keyword) => {
        const result = isMatch(content, keyword)
        return {
          keyword,
          result,
        }
      })
      .filter((item) => {
        if (!item.result.matched) return false

        // 传递匹配类型给过滤函数
        if (
          shouldSkipShortWord(
            item.keyword,
            content.original,
            item.result.confidence,
            item.result.type,
          )
        ) {
          return false
        }

        return true
      })

    const matchedKeywords = matches.map((item) => item.keyword)

    if (matches.length >= 2) {
      const avgConfidence =
        matches.reduce((sum, match) => sum + match.result.confidence, 0) / matches.length

      return {
        matched: true,
        confidence: avgConfidence * MATCH_TYPES.COMBINATION.multiplier,
        matchedKeywords,
      }
    }

    if (matches.length === 1) {
      const match = matches[0]
      if (match.result.confidence >= 0.95) {
        return {
          matched: true,
          confidence: match.result.confidence * 0.8,
          matchedKeywords,
        }
      }
    }

    const decompositionResult = wordDecompositionMatch(content, keywords)
    if (decompositionResult.matched) {
      if (
        !shouldSkipShortWord(
          decompositionResult.keyword,
          content.original,
          decompositionResult.confidence,
        )
      ) {
        return {
          matched: true,
          confidence: decompositionResult.confidence,
          matchedKeywords: [decompositionResult.keyword],
        }
      }
    }

    return { matched: false, confidence: 0, matchedKeywords: [] }
  }

  /** 关键词检测提醒 */
  const keywordDetectionReminder = (content: string) => {
    console.log('🔍 开始关键词检测:', content?.trim())
    if (!content?.trim()) return null

    const processedContent = preprocessText(content)
    let globalBestMatch = { keyword: '', confidence: 0, type: '', category: '' }

    for (const [category, keywords] of Object.entries(KEYWORDS_DETECTION.VIOLATION_KEYWORDS)) {
      if (!Array.isArray(keywords) || keywords.length === 0) continue

      const sortedKeywords = [...keywords]
        .filter((keyword) => keyword?.trim())
        .sort((a, b) => b.length - a.length)

      console.log(`🔍 检测分类: ${category}, 关键词数量: ${sortedKeywords.length}`)

      for (const keyword of sortedKeywords) {
        const result = isMatch(processedContent, keyword)
        if (result.matched) {
          // 传递匹配类型给过滤函数
          if (shouldSkipShortWord(keyword, content, result.confidence, result.type)) {
            console.log(
              `⏭️  跳过词汇: "${keyword}", 类型: ${result.type}, 原因: 长度/置信度/上下文过滤`,
            )
            continue
          }

          console.log(
            `✅ 匹配到关键词: "${keyword}", 置信度: ${result.confidence}, 类型: ${result.type}`,
          )
          if (result.confidence > globalBestMatch.confidence) {
            globalBestMatch = {
              keyword,
              confidence: result.confidence,
              type: result.type,
              category,
            }
          }
          if (result.confidence >= 0.95) break
        }
      }

      const combinationResult = checkCombinationMatch(processedContent, sortedKeywords)
      if (combinationResult.matched) {
        console.log(
          `✅ 组合匹配成功: ${combinationResult.matchedKeywords.join(', ')}, 置信度: ${combinationResult.confidence}`,
        )
        if (combinationResult.confidence > globalBestMatch.confidence) {
          globalBestMatch = {
            keyword: `组合关键词: ${combinationResult.matchedKeywords.join(', ')}`,
            confidence: combinationResult.confidence,
            type: MATCH_TYPES.COMBINATION.type,
            category,
          }
        }
      }
      if (globalBestMatch.confidence >= 1.0) break
    }

    console.log('🎯 最终检测结果:', globalBestMatch)
    if (globalBestMatch.confidence > 0.75) {
      // 再次提高最终阈值
      console.log(`🔔 关键词检测提醒: ${globalBestMatch.category}`)
      const currentUserMessage =
        KEYWORDS_DETECTION.VIOLATION_MESSAGES[userIntel.value.type]?.[globalBestMatch.category]
      return {
        category: globalBestMatch.category,
        keyword: globalBestMatch.keyword,
        message: currentUserMessage,
        messages: {
          [USER_TYPE.HR]:
            KEYWORDS_DETECTION.VIOLATION_MESSAGES[USER_TYPE.HR]?.[globalBestMatch.category],
          [USER_TYPE.APPLICANT]:
            KEYWORDS_DETECTION.VIOLATION_MESSAGES[USER_TYPE.APPLICANT]?.[globalBestMatch.category],
        },
        confidence: globalBestMatch.confidence,
        matchType: globalBestMatch.type,
        processedContent: processedContent.normalized,
      }
    }
    return null
  }

  return {
    keywordDetectionReminder,
  }
}
