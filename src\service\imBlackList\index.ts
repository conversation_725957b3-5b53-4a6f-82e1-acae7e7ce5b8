import { POSTPaging, POST } from '../index'
import { HttpRequestConfig } from 'luch-request'
import { imBlackListAddDataInt } from './types'

/** 列表数据查询接口 */
export const imBlackList = (data, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/imBlackList/queryList', data, config)

/** 添加本地黑名单 */
export const imBlackListAdd = (data: imBlackListAddDataInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/imBlackList/add', data, config)

/** 删除本地黑名单 */
export const imBlackListDelete = (data: imBlackListAddDataInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/imBlackList/delete', data, config)
