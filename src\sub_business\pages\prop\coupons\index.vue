<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '优惠卷',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <CustomNavBar title="优惠卷"></CustomNavBar>
    </template>
    <view class="coupons-box">
      <template v-for="section in couponSections" :key="section.key">
        <view v-if="section.coupons.length > 0" class="section-title">{{ section.title }}</view>
        <view v-if="section.coupons.length > 0" class="coupons-list">
          <CouponCard
            v-for="(coupon, index) in section.coupons"
            :key="`${section.key}-${index}`"
            :coupon="coupon"
            :status="section.key"
            @use-coupon="useCoupon"
          />
        </view>
      </template>
    </view>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CouponCard from '../../../components/CouponCard.vue'
import { queryMyCoupons } from '@/interPost/my'

enum CouponStatus {
  UNUSED = 0,
  USED = 1,
  EXPIRED = 2,
}

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const couponsData = ref([])

const couponSections = computed(() => {
  const sections = [
    {
      key: 'unused',
      title: '未使用',
      coupons: couponsData.value.filter((coupon) => coupon.status === CouponStatus.UNUSED),
    },
    {
      key: 'used',
      title: '已使用',
      coupons: couponsData.value.filter((coupon) => coupon.status === CouponStatus.USED),
    },
    {
      key: 'expired',
      title: '已过期',
      coupons: couponsData.value.filter((coupon) => coupon.status === CouponStatus.EXPIRED),
    },
  ]
  return sections
})

const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  const res: any = await queryMyCoupons({
    entity: {
      status: null,
    },
    orderBy: {},
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    couponsData.value = res.data.list
  }
  pagingRef.value.complete(res.data.list)
}

onShow(async () => {
  await uni.$onLaunched
  pagingRef.value.reload()
})

// 使用优惠券
const useCoupon = (coupon: any) => {
  console.log('使用优惠卷', coupon)
}
</script>

<style lang="scss" scoped>
.coupons-box {
  padding: 0rpx 40rpx;
}

// 分类标题样式
.section-title {
  margin: 40rpx 0 30rpx 0;
  font-size: 32rpx;
  font-weight: normal;
  color: #333333;

  &:first-child {
    margin-top: 20rpx;
  }
}

// 优惠券列表样式
.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}
</style>
