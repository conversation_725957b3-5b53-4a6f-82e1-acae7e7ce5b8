<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="历史面试"></CustomNavBar>
    <z-paging
      ref="pagingRef"
      v-model="pageData"
      :fixed="false"
      :paging-style="pageStyle"
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
      safe-area-inset-bottom
      @query="queryList"
    >
      <view class="pageList">
        <view v-if="pageData.length > 0" class="c-#888 text-32rpx p-b-20rpx p-l-40rpx"></view>
        <view v-for="(item, index) in pageData" :key="index">
          <InterviewCard :item="item" :show-time="shouldShowTime(index)" />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryHistoryList } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { truncateText } from '@/utils/util'
import { getCustomBar } from '@/utils/storage'
import InterviewCard from '@/resumeRelated/component/interviewCard.vue'

const { pagingRef, pageData, pageStyle } = usePaging({
  style: {
    padding: '50rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '40rpx',
  },
})
const customBar = ref(null)

// 判断是否显示时间
const shouldShowTime = (index: number) => {
  if (index === 0) return true
  const currentItem = pageData.value[index]
  const prevItem = pageData.value[index - 1]
  if (!currentItem?.agreeTime || !prevItem?.agreeTime) return true

  // 比较日期和时间是否完全相同（精确到分钟）
  const currentDateTime = currentItem.agreeTime.slice(0, 16) // YYYY-MM-DD HH:MM
  const prevDateTime = prevItem.agreeTime.slice(0, 16)

  // 只有日期和时间完全相同才隐藏，不同时间点要显示
  return currentDateTime !== prevDateTime
}

// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}

const queryList = async () => {
  const res: any = await queryHistoryList()
  if (res.code === 0) {
    res.data &&
      res.data.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryStart =
          ele.workSalaryStart === 0 ? '面议' : numberTokw(ele.workSalaryStart + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
        if (!ele.headImgUrl) {
          ele.headImgUrl =
            ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
        }
      })

    pagingRef.value.complete(res.data)
  }
}
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
</style>
