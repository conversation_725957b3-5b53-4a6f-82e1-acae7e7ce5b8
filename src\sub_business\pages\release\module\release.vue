<template>
  <view class="flex justify-center mt-40rpx px-40rpx">
    <view class="w-full flex flex-col items-center relative">
      <!-- <view
        class="w-182rpx h-182rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-full center relative z-10"
      >
        <wd-img width="158rpx" height="158rpx" round :src="releaseLogo" />
      </view> -->
      <view
        class="bg-white shadow-[4px_4px_16.5px_0px_rgba(0,0,0,0.1)] rounded-20rpx w-full mt-0rpx p-[20rpx_40rpx_40rpx] relative"
      >
        <view class="w-120rpx job-type-picker absolute top-70rpx right-40rpx z-80">
          <wd-picker
            :columns="jobTypeOptions"
            label-key="text"
            v-model="releasePostModel.jobType"
            class="rotate"
            custom-class="bg-#F0F0F0 rounded-8rpx"
          />
        </view>
        <view class="absolute bottom-250rpx right-40rpx z-99">
          <view class="flex items-center">
            <wd-checkbox
              v-model="salaryStructure"
              @change="changeSalaryStructure"
              v-if="salaryStructureShow"
            ></wd-checkbox>
            <text
              v-if="salaryStructureShow"
              class="c-#333333 text-28rpx"
              @click="openSalaryStructure"
            >
              薪资结构
            </text>
          </view>
        </view>
        <view
          class="absolute bottom-200rpx right-40rpx z-50 flex items-center gap-10rpx"
          v-if="salaryStructureShow && releasePostModel.salaryFixed && releasePostModel.salaryFloat"
        >
          <view class="text-22rpx c-#333 m-r-5rpx">
            固定薪资：{{ releasePostModel.salaryFixed }}
          </view>
          <view class="text-22rpx c-#333">浮动薪资：{{ releasePostModel.salaryFloat }}</view>
        </view>
        <wd-config-provider :themeVars="themeVars">
          <wd-form ref="formRef" :model="releasePostModel" error-type="toast">
            <!-- 其他表单项 -->
            <wd-cell
              v-for="(item, key) in filteredFormItemListWithoutExperienceAndEducational"
              :key="`form-item-${key}`"
              :prop="item.prop"
              :rules="item.rules"
              vertical
              clickable
              custom-class="pt-28rpx pb-20rpx border-b-1px border-b-dashed border-b-[#BFBFBF]"
              @click="handleFormItemAction(item)"
            >
              <template #title>
                <text class="c-#333333 text-28rpx">{{ item.label }}</text>
              </template>
              <wd-input
                v-if="
                  item.type === 'input' &&
                  !['positionKeyList', 'positionBenefitList'].includes(item.prop)
                "
                v-model="releasePostModel[item.prop as string]"
                :placeholder="item.placeholder"
                no-border
                readonly
              />
              <wd-input
                v-else-if="item.type === 'input' && item.prop === 'positionKeyList'"
                v-model="positionKeyListFormatted"
                :placeholder="item.placeholder"
                suffixIcon="arrow-right"
                no-border
                readonly
              />
              <wd-input
                v-else-if="item.type === 'input' && item.prop === 'positionBenefitList'"
                v-model="positionBenefitListFormatted"
                :placeholder="item.placeholder"
                suffixIcon="arrow-right"
                no-border
                readonly
              />
              <wd-picker
                :z-index="100"
                class="m-wd-picker__arrow-none"
                v-model="releasePostModel[item.prop]"
                v-else-if="item.type === 'picker' && item.prop == 'workSalary'"
                :placeholder="item.placeholder"
                :columns="salaryColumns"
                :column-change="onSalaryColumnChange"
                :display-format="salaryDisplayFormat"
              />

              <wd-picker
                :z-index="100"
                v-model="releasePostModel[item.prop]"
                v-else
                :placeholder="item.placeholder"
                :columns="item.columns"
              />
            </wd-cell>
            <!-- 工作经验和学历一行显示 -->
            <wd-cell
              prop="workExperience-workEducational-recruitingNum"
              :rules="[]"
              vertical
              clickable
              custom-class="pt-28rpx pb-20rpx border-b-1px border-b-dashed border-b-[#BFBFBF]"
            >
              <view class="flex justify-between w-100">
                <view class="w-33% updata-w relative my-line">
                  <view class="c-#333333 text-28rpx text-center">
                    经验要求
                    <text class="i-carbon-triangle-down-solid text-16rpx c-#333" />
                  </view>
                  <wd-picker
                    :z-index="100"
                    class="cell-left"
                    v-model="releasePostModel['workExperience']"
                    :placeholder="
                      formItemList.find((item) => item.prop === 'workExperience').placeholder
                    "
                    :columns="formItemList.find((item) => item.prop === 'workExperience').columns"
                  />
                </view>
                <view class="w-33% updata-w relative my-line">
                  <view class="c-#333333 text-28rpx text-center">
                    学历要求
                    <text class="i-carbon-triangle-down-solid text-16rpx c-#333" />
                  </view>

                  <wd-picker
                    class="cell-left"
                    :z-index="100"
                    v-model="releasePostModel['workEducational']"
                    :placeholder="
                      formItemList.find((item) => item.prop === 'workEducational').placeholder
                    "
                    :columns="formItemList.find((item) => item.prop === 'workEducational').columns"
                  />
                </view>
                <view class="w-33% updata-w">
                  <view class="c-#333333 text-28rpx text-center">
                    招聘人数
                    <text class="i-carbon-triangle-down-solid text-16rpx c-#333" />
                  </view>
                  <wd-picker
                    class="cell-left"
                    :z-index="100"
                    v-model="releasePostModel['recruitingNum']"
                    :placeholder="
                      formItemList.find((item) => item.prop === 'recruitingNum').placeholder
                    "
                    :columns="formItemList.find((item) => item.prop === 'recruitingNum').columns"
                  />
                </view>
              </view>
            </wd-cell>
          </wd-form>
        </wd-config-provider>
      </view>
      <view
        class="m-t-40rpx m-b-40rpx h-168rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx flex items-center gap-20rpx px-36rpx w-100"
        @tap="handleSelectWorkerAddress"
      >
        <view class="flex flex-col flex-1 gap-16rpx">
          <view class="flex items-center gap-10rpx">
            <text class="c-#333333 text-24rpx">工作地址</text>
            <view class="flex items-center gap-4rpx" v-if="!releaseActiveAddress?.status">
              <wd-icon name="info-circle" size="34rpx" color="#FF0000" />
              <text class="c-#FF0000 text-26rpx">未认证</text>
            </view>
          </view>
          <text class="line-clamp-1 c-#333333 text-26rpx">
            {{ releaseActiveAddress?.address ?? '请选择工作地址' }}
          </text>
        </view>
        <wd-icon name="arrow-right" size="30rpx" color="#8A8A8A" />
      </view>
    </view>
  </view>
  <wd-popup
    v-model="showDialog"
    :close-on-click-modal="false"
    position="bottom"
    :z-index="106"
    custom-style="height: 300px;border-radius: 20px 20px 0px 0px;padding: 40rpx;"
  >
    <view class="flex items-center justify-between">
      <view @click="handleCancelSalaryStructure">取消</view>
      <view>薪资结构（元/月）</view>
      <view class="text-#4d8fff" @click="handleConfirmSalaryStructure">确定</view>
    </view>
    <view class="flex flex-col gap-20rpx">
      <view
        class="flex items-center justify-between gap-10rpx m-b-40rpx m-t-60rpx border-b-1rpx border-b-dashed border-b-[#BFBFBF] p-b-30rpx"
      >
        <text class="c-#333333 text-28rpx w-150rpx">固定薪资</text>
        <wd-input
          class="flex-1 salary-input"
          type="number"
          no-border
          :maxlength="7"
          v-model.number="releasePostModel.salaryFixed"
          placeholder="请填写固定薪资"
        />
      </view>
      <view
        class="flex items-center justify-between gap-10rpx border-b-1rpx border-b-dashed border-b-[#BFBFBF] p-b-30rpx"
      >
        <text class="c-#333333 text-28rpx w-150rpx">浮动薪资</text>
        <wd-input
          class="flex-1 salary-input"
          type="number"
          no-border
          :maxlength="7"
          v-model.number="releasePostModel.salaryFloat"
          placeholder="请填写浮动薪资"
        />
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { DICT_IDS } from '@/enum'
import { useLoginStore } from '@/store'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { ReleaseStep } from '@/sub_business/types/release'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { FormItemRule } from 'wot-design-uni/components/wd-form/types'
import type {
  ColumnItem,
  PickerViewColumnChange,
} from 'wot-design-uni/components/wd-picker-view/types'
import type { PickerDisplayFormat } from 'wot-design-uni/components/wd-picker/types'
import type { hrPositionAddDataInt } from '@/service/hrPosition/types'
import type { DictOption } from '@/hooks/common/useDictionary'
import releaseLogo from '@/sub_business/static/release/logo.png'
import { hrCompanyWorkAddressQueryPassList } from '@/service/hrCompanyWorkAddress'
const { releaseActiveAddress } = useReleasePost()
const positionName = ref('')
interface FormItem {
  prop: keyof hrPositionAddDataInt
  label: string
  type: 'input' | 'picker' | 'select' | 'textarea'
  placeholder: string
  rules: FormItemRule[]
  columns?: (string | number | ColumnItem | (string | number | ColumnItem)[])[]
  action?: () => void
}
const emits = defineEmits<{
  (e: 'nextStep', value: ReleaseStep): void
}>()
async function fetchHrCompanyWorkAddressQueryPassList() {
  // const { data } = await hrCompanyWorkAddressQueryPassList({
  //   entity: {},
  //   page: 1,
  //   size: 1,
  // })
  // const [first] = data.list
  // if (first?.id) {
  //   releaseActiveAddress.value = first
  //   releasePostModel.value.companyWorkAddressId = first.id
  // }
  releasePostModel.value.companyWorkAddressId = releaseActiveAddress.value.id
}
const showDialog = ref(false)
const salaryStructure = ref(false)
const salaryStructureShow = ref(false)
const loginStore = useLoginStore()
const { releasePostModel, releasePositionKey, releasePositionMark, releaseSalaryColumns } =
  useReleasePost()
const { getDictOptions } = useDictionary()
const { formRef } = useForm()
const themeVars: ConfigProviderThemeVars = {
  cellPadding: '0',
  cellWrapperPadding: '0',
  cellVerticalTop: '4rpx',
  cellTitleFs: '26rpx',
  cellTapBg: 'transparent',
  inputPlaceholderColor: '#888888',
}
const props = defineProps<{
  positionId: number | string | null
  salaryStructureProp: boolean
}>()
const jobTypeOptions = ref<DictOption[]>([])
const formItemList = reactive<FormItem[]>([
  {
    prop: 'positionName',
    label: '岗位名称',
    type: 'input',
    placeholder: '请选择岗位，如"市场专员"',
    rules: [{ required: true, message: '请填写岗位名称' }],
    action: () => {
      const positionData = releasePostModel.value.positionCode
        ? {
            expectedPositions: releasePostModel.value.positionName,
            expectedPositionsCode: releasePostModel.value.positionCode,
          }
        : {}
      loginStore.setpositionData(positionData)
      if (!releasePostModel.value.id) {
        uni.navigateTo({
          url: '/loginSetting/category/career',
        })
      }
    },
  },
  {
    prop: 'positionMarkName',
    label: '岗位标签',
    type: 'picker',
    placeholder: '请选择岗位标签',
    rules: [{ required: false, message: '请选择岗位标签' }],
    columns: [],
  },
  {
    prop: 'positionKeyList',
    label: '岗位关键词',
    type: 'input',
    placeholder: '被选中的关键词将突出展示给应聘者',
    rules: [
      {
        required: true,
        message: '请至少选择一个岗位关键词',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请至少选择一个岗位关键词'))
          }
        },
      },
    ],
    action: () => {
      if (!releasePostModel.value.positionCode) {
        uni.showToast({
          title: '请先选择岗位名称',
          icon: 'none',
        })
        return
      }
      emits('nextStep', ReleaseStep.KEYWORDS)
    },
  },
  {
    prop: 'positionBenefitList',
    label: '岗位待遇',
    type: 'input',
    placeholder: '添加待遇标签将突出展示给应聘者',
    rules: [
      {
        required: true,
        message: '添加待遇标签将突出展示给应聘者',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请至少选择一个岗位待遇'))
          }
        },
      },
    ],
    action: () => {
      emits('nextStep', ReleaseStep.SALARY)
    },
  },
  {
    prop: 'workExperience',
    label: '',
    type: 'picker',
    placeholder: '请选择工作经验',
    rules: [{ required: true, message: '请选择工作经验' }],
    columns: [],
  },
  {
    prop: 'workEducational',
    label: '',
    type: 'picker',
    placeholder: '请选择学历要求',
    rules: [{ required: true, message: '请选择学历要求' }],
    columns: [],
  },
  {
    prop: 'recruitingNum',
    label: '',
    type: 'picker',
    placeholder: '请选择',
    rules: [{ required: true, message: '请选择招聘人数' }],
    columns: [],
  },
  {
    prop: 'positionDesc',
    label: '岗位描述',
    type: 'input',
    placeholder: '请填写岗位的详细描述',
    rules: [{ required: true, message: '请填写工作描述' }],
    action: () => {
      if (!releasePostModel.value.positionCode) {
        uni.showToast({
          title: '请先选择职位名称',
          icon: 'none',
        })
        return
      }
      emits('nextStep', ReleaseStep.DESCRIBE)
    },
  },
  {
    prop: 'workSalary',
    label: '薪资范围',
    type: 'picker',
    placeholder: '请选择合理的薪资范围',
    rules: [
      {
        required: true,
        message: '请选择合理的薪资范围',
        validator: (value) => {
          if (CommonUtil.isArray(value) && value.length >= 3) {
            return Promise.resolve()
          } else {
            return Promise.reject(new Error('请选择完整的薪资范围（起始薪资-结束薪资-薪资月数）'))
          }
        },
      },
    ],
    columns: [],
  },
])
function openSalaryStructure() {
  showDialog.value = true
}
// 薪资数据 - 3级联动：起始薪资、结束薪资、薪资月数
const salaryData = {
  '1k': ['2k', '3k', '4k', '5k', '6k'],
  '2k': ['3k', '4k', '5k', '6k', '7k'],
  '3k': ['4k', '5k', '6k', '7k', '8k'],
  '4k': ['5k', '6k', '7k', '8k', '9k'],
  '5k': ['6k', '7k', '8k', '9k', '10k'],
  '6k': ['7k', '8k', '9k', '10k', '11k'],
  '7k': ['8k', '9k', '10k', '11k', '12k'],
  '8k': ['9k', '10k', '11k', '12k', '13k'],
  '9k': ['10k', '11k', '12k', '13k', '14k'],
  '10k': ['11k', '12k', '13k', '14k', '15k'],
  '11k': ['12k', '13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k'],
  '12k': ['13k', '14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k'],
  '13k': ['14k', '15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k'],
  '14k': ['15k', '16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k'],
  '15k': ['16k', '17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k'],
  '16k': ['17k', '18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k'],
  '17k': ['18k', '19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k'],
  '18k': ['19k', '20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k'],
  '19k': ['20k', '21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k'],
  '20k': ['21k', '22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k'],
  '21k': ['22k', '23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k'],
  '22k': ['23k', '24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k'],
  '23k': ['24k', '25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k'],
  '24k': ['25k', '26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k'],
  '25k': ['26k', '27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k'],
  '26k': ['27k', '28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k'],
  '27k': ['28k', '29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k'],
  '28k': ['29k', '30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k'],
  '29k': ['30k', '31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k'],
  '30k': ['31k', '32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k'],
  '31k': ['32k', '33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k'],
  '32k': ['33k', '34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k'],
  '33k': ['34k', '35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k'],
  '34k': ['35k', '36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k'],
  '35k': ['36k', '37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k'],
  '36k': ['37k', '38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k'],
  '37k': ['38k', '39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k'],
  '38k': ['39k', '40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k'],
  '39k': ['40k', '41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k'],
  '40k': ['41k', '42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k'],
  '41k': ['42k', '43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k'],
  '42k': ['43k', '44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k'],
  '43k': ['44k', '45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k'],
  '44k': ['45k', '46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k'],
  '45k': ['46k', '47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k'],
  '46k': ['47k', '48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k'],
  '47k': ['48k', '49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k'],
  '48k': ['49k', '50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k'],
  '49k': ['50k', '51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k'],
  '50k': ['51k', '52k', '53k', '54k', '55k', '56k', '57k', '58k', '59k', '60k'],
}

// 薪资月数选项
const salaryMonths = ['12薪', '13薪', '14薪', '15薪', '16薪']
// 修改列变化处理 - 3级联动
const onSalaryColumnChange = (picker, values, columnIndex, resolve) => {
  if (columnIndex === 0) {
    // 第一列变化时，更新第二列（结束薪资）
    const selected = values[0]?.value || '1k'
    picker.setColumnData(
      1,
      salaryData[selected].map((item: any) => ({ label: item, value: item })),
    )
    // 同时更新第三列（薪资月数）
    picker.setColumnData(
      2,
      salaryMonths.map((item: any) => ({ label: item, value: item })),
    )
    resolve()
  } else if (columnIndex === 1) {
    // 第二列变化时，更新第三列（薪资月数）
    picker.setColumnData(
      2,
      salaryMonths.map((item: any) => ({ label: item, value: item })),
    )
    resolve()
  }
}

const salaryColumns = ref([
  Object.keys(salaryData).map((item) => ({ label: item, value: item })),
  salaryData['1k'].map((item) => ({ label: item, value: item })),
  salaryMonths.map((item) => ({ label: item, value: item })),
])
// 发布岗位
function handleConfirmSalaryStructure() {
  const { salaryFixed, salaryFloat, workSalaryBegin, workSalaryEnd } = releasePostModel.value
  if (!salaryFixed || !salaryFloat) {
    uni.showToast({
      title: '请填写薪资结构',
      icon: 'none',
    })
    return
  }
  const salaryTotal = Number(salaryFixed) + Number(salaryFloat)
  if (salaryTotal < workSalaryBegin || salaryTotal > workSalaryEnd) {
    uni.showToast({
      title: '薪资结构不能超出薪资范围',
      icon: 'none',
    })
    return
  }
  showDialog.value = false
}
function handleCancelSalaryStructure() {
  releasePostModel.value.salaryFixed = null
  releasePostModel.value.salaryFloat = null
  showDialog.value = false
}
function handleSelectWorkerAddress() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_business/pages/AddressCenter/index', {
      source: 'release',
    }),
  })
}
function changeSalaryStructure(value: any) {
  salaryStructure.value = value.value
  releasePostModel.value.salaryType = value.value ? 1 : 0
  if (salaryStructure.value) {
    showDialog.value = true
  } else {
    showDialog.value = false
  }
}
onMounted(() => {
  positionName.value = releasePostModel.value.positionMarkName
    ? releasePostModel.value.positionMarkName
    : releasePostModel.value.positionName
})
const filteredFormItemList = computed(() => {
  return formItemList.filter((item) => {
    if (item.prop === 'positionMarkName') {
      return item.columns && item.columns.length > 0
    }
    return true
  })
})
const filteredFormItemListWithoutExperienceAndEducational = computed(() => {
  console.log(filteredFormItemList.value, 'filteredFormItemList.value')
  return filteredFormItemList.value.filter(
    (item) => !['workExperience', 'workEducational', 'recruitingNum'].includes(item.prop),
  )
})
const workExperienceItem = computed(() =>
  formItemList.find((item) => item.prop === 'workExperience'),
)
const workSalaryItem = computed(() => formItemList.find((item) => item.prop === 'workSalary'))
const salaryColumnsFormatted = computed(() => {
  const result: Record<string, ColumnItem[]> = { '0': [] }
  Object.entries(releaseSalaryColumns).forEach(([startSalary, endSalaries]) => {
    result['0'].push({
      label: startSalary,
      value: startSalary,
    })
    if (Array.isArray(endSalaries)) {
      result[startSalary] = endSalaries.map((endSalary) => ({
        label: endSalary,
        value: endSalary,
      }))
    }
  })
  return result
})
const positionKeyListFormatted = computed({
  get: () => {
    const value = releasePostModel.value.positionKeyList
    return value?.join(',') || ''
  },
  set: (val: string) => {
    releasePostModel.value.positionKeyList = val ? val.split(',') : []
  },
})

const positionBenefitListFormatted = computed({
  get: () => {
    const value = releasePostModel.value.positionBenefitList
    return value?.join(',') || ''
  },
  set: (val: string) => {
    releasePostModel.value.positionBenefitList = val ? val.split(',') : []
  },
})

function handleFormItemAction(item: FormItem) {
  item?.action?.()
}

const handleSelectJobType = (value: number) => {
  releasePostModel.value.jobType = value
}
const fetchJobTypeOptions = async () => {
  const list = await getDictOptions(DICT_IDS.WORK_TYPE)
  jobTypeOptions.value = list.map((item) => ({
    text: item.text,
    value: item.value,
  }))
  try {
    const [{ value }] = jobTypeOptions.value
    releasePostModel.value.jobType = value as number
  } catch (error) {}
}
const onChangeSalaryColumn: PickerViewColumnChange = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]
  if (columnIndex === 0) {
    pickerView.setColumnData(1, salaryColumnsFormatted.value[item.value])
  }
  resolve()
}
const salaryDisplayFormat: PickerDisplayFormat = (items) => {
  return Array.from(new Set(items.map((item: ColumnItem) => item.label))).join('-')
}
const fetchWorkEducationalOptions = async () => {
  const workEducational = formItemList.find((item) => item.prop === 'workEducational')
  const dictData = await getDictOptions(DICT_IDS.EDUCATION_B)
  workEducational.columns = dictData.map((item) => ({
    label: item.text as string,
    value: item.value,
  }))
}
const fetchPositionMarkCodeOptions = () => {
  const positionMarkName = formItemList.find((item) => item.prop === 'positionMarkName')
  positionMarkName.columns = releasePositionMark.value.map((item) => item.name)
}
const fetchWorkExperienceOptions = async () => {
  const workExperience = formItemList.find((item) => item.prop === 'workExperience')
  const dictData = await getDictOptions(DICT_IDS.EXPERIENCE_REQUIREMENT)
  workExperience.columns = dictData.map((item) => ({
    label: (item.text as AnyObject).label,
    value: item.value,
    ...(item.text as AnyObject),
  }))
}
const fetchRecruitingNumOptions = async () => {
  const recruitingNum = formItemList.find((item) => item.prop === 'recruitingNum')
  const dictData = await getDictOptions(DICT_IDS.RECRUIT_NUMBER)
  recruitingNum.columns = dictData.map((item) => ({
    label: item.text as string,
    value: item.value,
  }))
}
const fetchSalaryOptions = () => {
  const workSalary = formItemList.find((item) => item.prop === 'workSalary')
  const [begin, _end, _months] = releasePostModel.value.workSalary || []
  workSalary.columns = [
    salaryColumnsFormatted.value[0],
    salaryColumnsFormatted.value[begin ?? '1k'],
    salaryMonths.map((item) => ({ label: item, value: item })),
  ]
}
const releaseValidate = async () => {
  return await formRef.value?.validate()
}
onMounted(() => {
  fetchJobTypeOptions()
  fetchWorkEducationalOptions()
  fetchWorkExperienceOptions()
  fetchSalaryOptions()
  fetchPositionMarkCodeOptions()
  fetchRecruitingNumOptions()
})

watch(
  () => loginStore.positionObj,
  (positionObj) => {
    if (!positionObj?.expectedPositionsCode) return
    releasePostModel.value.positionName = positionObj.expectedPositions
    releasePostModel.value.positionCode = positionObj.expectedPositionsCode
    releasePositionKey.value = positionObj.expectedPositionsSearchKey
    releasePositionMark.value = positionObj.fourthLevelPositions || []
    fetchPositionMarkCodeOptions()
  },
  {
    deep: true,
  },
)
watch(
  [() => workExperienceItem.value, () => releasePostModel.value.workExperience],
  ([item, experience]) => {
    const columns = (item.columns as ColumnItem[]) || []
    const selectedOption = columns.find((item) => item.value === experience)
    if (!selectedOption) return
    releasePostModel.value.workExperienceStart = selectedOption.workExperienceStart
    releasePostModel.value.workExperienceEnd = selectedOption.workExperienceEnd
  },
  {
    deep: true,
  },
)
watch([() => releasePostModel.value.recruitingNum], ([num]) => {
  if (!num) return
  releasePostModel.value.recruitingNum = num
})
watch([() => releasePostModel.value.positionMarkName], ([name]) => {
  if (!name) return
  releasePostModel.value.positionMarkCode =
    releasePositionMark.value.find((item) => item.name === name)?.code ?? null
})
watch(
  [() => workSalaryItem.value, () => releasePostModel.value.workSalary],
  ([_item, salary]) => {
    if (!salary.length) return
    const [begin, end, months] = salary
    releasePostModel.value.workSalaryBegin = parseInt(begin, 10) * 1000
    releasePostModel.value.workSalaryEnd = parseInt(end, 10) * 1000
    // 保存薪资月数信息，将"12薪"转换为12
    if (months) {
      // 确保 months 是字符串类型再调用 replace
      const monthsStr = String(months)
      releasePostModel.value.salaryMonths = monthsStr
        ? parseInt(monthsStr.replace('薪', ''), 10)
        : null
    }
  },
  {
    deep: true,
  },
)
watch(
  [() => releasePostModel.value.workSalaryBegin, () => releasePostModel.value.workSalaryEnd],
  ([workSalaryBegin, workSalaryEnd]) => {
    if (workSalaryBegin && workSalaryEnd) {
      console.log('workSalaryBegin', workSalaryBegin)
      salaryStructureShow.value = true
    } else {
      console.log('workSalaryBegin', workSalaryBegin)
      salaryStructureShow.value = false
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

onShow(() => {
  setTimeout(() => {
    salaryStructure.value = props.salaryStructureProp
  }, 1000)

  fetchHrCompanyWorkAddressQueryPassList()
})
defineExpose({
  releaseValidate,
})
</script>

<style lang="scss" scoped>
:deep(.wd-picker__value) {
  margin-right: 0rpx !important;
}
.salary-input .uni-input-placeholder {
  text-align: right !important;
}
// :deep(.wd-icon-arrow-right) {
//   display: none !important;
// }
:deep(.m-wd-picker__arrow-none) {
  .wd-icon-arrow-right {
    display: none !important;
  }
}
// :deep(.cell-left) {
//   .wd-cell__wrapper {
//     text-align: center !important;
//   }
// }
:deep(.rotate) {
  .wd-cell__wrapper {
    padding: 0rpx !important;
  }
  .wd-icon-arrow-right {
    width: 30rpx;
    transform: rotate(90deg);
  }
}
:deep(.salary-input) {
  .uni-input-input {
    text-align: right !important;
  }
}
:deep(.job-type-picker) {
  .wd-picker__value {
    margin-right: 0rpx !important;
    font-size: 24rpx !important;
    text-align: center !important;
  }
  .wd-picker__cell {
    padding: 0rpx 10rpx !important;
    background: #f0f0f0 !important;
    border-radius: 20rpx !important;
  }
  :deep(.wd-icon-arrow-right) {
    display: block !important;
  }
}
:deep(.updata-w .wd-checkbox__label) {
  margin-left: 5rpx !important;
}

:deep(.updata-w) {
  .wd-cell__value--left {
    text-align: center !important;
  }
  .wd-icon {
    display: none !important;
    margin-right: 0rpx !important;
  }
  .wd-cell__wrapper {
    text-align: center !important;
  }
  .wd-picker__cell {
    text-align: center !important;
  }
  :deep(.wd-picker__valu) {
    margin-right: 0rpx !important;
  }
}
:deep(.my-line) {
  &::after {
    position: absolute;
    bottom: 0;
    left: 100%;
    width: 1rpx;
    height: 80rpx;
    content: '';
    background-color: #bfbfbf;
  }
}
:deep(.my-line1) {
  &::after {
    position: absolute;
    bottom: 0;
    left: 64%;
    width: 1rpx;
    height: 80rpx;
    content: '';
    background-color: #bfbfbf;
  }
}
</style>
