<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :loading-more-enabled="false" :paging-style="pageStyle" :refresher-enabled="false">
    <template #top>
      <CustomNavBar title="我的在线简历">
        <template v-slot:right>
          <view class="CustomNavBar-right" @click="goPreview">预览</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="onlineRes" @click="closeOutside">
      <view class="onlineRes-list flex-between">
        <view class="onlineRes-left">
          <view class="onlineRes-info flex-c">
            <view class="flex-c m-b-10rpx">
              <view class="name">{{ onlineObj.userName }}</view>
            </view>

            <view
              v-if="onlineObj.isApplicant == 0"
              class="onlineRes-rz flex-c"
              @click="goCheackInfo"
            >
              <view class="name-rz nomal">未认证</view>
              <wd-icon color="#FF0000" name="warning" size="15px"></wd-icon>
            </view>
          </view>
          <view class="onlineRes-my flex items-center m-b-15rpx m-t-6rpx">
            <view class="onlineRes-my-item flex items-center">
              <wd-img :height="16" :src="birthdayIcon" :width="16" class="wd-img" />
              <text class="c-#333333 text-28rpx">{{ onlineObj.age || '**' }} 岁</text>
            </view>

            <view class="onlineRes-my-item flex items-center">
              <wd-img :height="16" :src="icons" :width="16" class="wd-img" />
              <view class="c-#333333 text-28rpx">
                {{
                  onlineObj?.workYear > 0 && onlineObj?.workYear <= 10
                    ? onlineObj.workYear + '年'
                    : onlineObj.workYear > 10
                      ? '10年以上'
                      : '**'
                }}
              </view>
            </view>

            <view class="onlineRes-my-item flex items-center">
              <wd-img :height="16" :src="educationIcon" :width="16" class="wd-img" />
              <view class="c-#333333 text-28rpx">{{ onlineObj.xueLi || '**' }}</view>
            </view>
          </view>
          <view class="onlineRes-connect flex-c">
            <view class="flex-c m-right">
              <image
                class="onlineRes-connect-img"
                src="@/resumeRelated/img/Group_1171274957.png"
              ></image>
              <view class="onlineRes-connect-name">{{ onlineObj.telephone || '***' }}</view>
            </view>
            <view class="flex-c">
              <image
                class="onlineRes-connect-img-1"
                src="@/resumeRelated/img/Group_1171274958.png"
              ></image>
              <view v-if="onlineObj.wxCode" class="onlineRes-connect-name">
                {{ onlineObj.wxCode || '***' }}
              </view>
              <view v-else class="onlineRes-connect-name c-#4d8fff" @click="goWxUpdata">***</view>
            </view>
          </view>
        </view>
        <view class="onlineRes-right flex justify-right">
          <wd-img
            :class="onlineObj.sex === 1 ? 'border-boy' : 'border-griy'"
            :enable-preview="true"
            :height="60"
            :src="onlineObj.headImgUrl"
            :width="60"
            round
          />
        </view>
      </view>
      <view class="resume-list flex-between">
        <view class="onlineRes-job-left flex-1">
          <view class="onlineRes-title">求职状态</view>
          <wd-picker
            v-model="onlineObj.seekStatus"
            :columns="qzList"
            class="custom-view-class"
            label=""
            @confirm="submitJob"
          />
        </view>
        <view class="">
          <wd-icon color="#333333" name="chevron-right" size="18px"></wd-icon>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goMyAdvantage">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">个人亮点</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              v-if="!onlineObj.myLights"
              class="p-l-20rpx"
              color="#000000"
              name="add-circle1"
              size="40rpx"
            ></wd-icon>
          </view>
        </view>
        <view v-if="onlineObj.myLights" class="text-container" @click="goMyAdvantage">
          <view class="mt-10rpx flex-between flex items-center">
            <view
              class="text-pre-wrap text-28rpx c-#333 my-lights-text"
              @click="goEditMyAdvantage(onlineObj.myLights)"
            >
              {{ myLightsText }}
              <text
                v-if="showMyLightsMore"
                class="view-detail text-28rpx"
                style="color: #589bff"
                @click.stop="showMyLightsDetail"
              >
                展开
              </text>
              <text
                v-if="showMyLightsCollapse && !showMyLightsMore"
                class="view-detail text-28rpx"
                style="color: #589bff"
                @click.stop="showMyLightsShrink"
              >
                收起
              </text>
            </view>
            <view class="jobExpectations-exepress-right">
              <wd-icon color="#333333" name="chevron-right" size="40rpx"></wd-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="handleAddJobExpectation">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx p-b-10rpx">求职期望</view>
            <wd-tooltip content="完善求职期望，为您智能匹配更多优质岗位!" placement="right-start">
              <wd-icon color="#000000" name="help-circle" size="36rpx"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right">
            <wd-icon class="p-l-10rpx" color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>
        <view
          v-for="(item, index) in jobIntentionListObj"
          :key="index"
          @click="goEditSeekEmployment(item)"
        >
          <!--          <view class="jobExpectations-qw text-28rpx">-->
          <!--            {{ item.jobType === 1 ? '全职期望' : '兼职期望' }}-->
          <!--          </view>-->
          <view class="jobExpectations-exepress flex-between mt-10rpx">
            <view class="jobExpectations-exepress-left">
              <view class="jobExpectations-exepress-list">
                <view class="flex-c">
                  <view
                    :class="
                      item.jobType === 1
                        ? 'full-time'
                        : item.jobType === 2 || item.jobType === 3
                          ? 'full-time-other'
                          : ''
                    "
                    class="text-28rpx font-w-500"
                  >
                    {{ item.jobTypeLabel }}
                  </view>
                  <view class="text-name text-salary text-30rpx">
                    {{ item.expectedPositions }}
                  </view>
                  <view class="text-name text-salary text-28rpx">
                    <text>
                      {{
                        item.salaryExpectationStart === '面议'
                          ? '薪资面议'
                          : item.salaryExpectationStart
                      }}
                    </text>
                    <text v-if="item.salaryExpectationEnd">-</text>
                    <text v-if="item.salaryExpectationEnd">{{ item.salaryExpectationEnd }}</text>
                    <text v-if="item.salaryExpectationEnd && item.salaryExpectationStart">/月</text>
                  </view>
                </view>
                <view class="flex-c">
                  <view class="text-position">{{ item.provinceName }}</view>
                  <view class="text-position text-denery">
                    {{ item.expectedIndustry === '不限' ? '行业不限' : item.expectedIndustry }}
                  </view>
                </view>
              </view>
            </view>
            <view class="jobExpectations-exepress-right">
              <wd-icon color="#333333" name="chevron-right" size="40rpx"></wd-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goEducation">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">教育经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon class="p-l-20rpx" color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>
        <view
          v-for="(item, index) in onlineObj.educationsList"
          :key="index"
          class="education-list flex-between mt-20rpx"
          @click="goEducationEdit(item)"
        >
          <view class="education-left flex-c">
            <view class="education-left-img"></view>
            <view class="education-left-xl">
              <view class="flex items-center m-b-4rpx">
                <view class="text-30rpx c-#333 font-w-500">{{ item.school }}</view>
                <view class="education-left-xl-subname text-28rpx c-#333333 m-l-20rpx">
                  {{ item.qualificationLabel }}
                </view>
              </view>

              <view class="m-t-10rpx">
                <view class="flex items-center">
                  <view
                    class="education-left-xl-subname text-28rpx c-#333333"
                    style="display: inline"
                  >
                    {{ item.major }}
                  </view>
                  <view class="education-left-xl-subname text-28rpx c-#333333 m-l-40rpx">
                    {{ item.startTime.substring(0, 4) }}年-{{ item.endTime.substring(0, 4) }}年
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="education-right flex-between">
            <view></view>
            <view class="flex-c">
              <view class="time">
                <!-- {{ item.startTime.substring(0, 4) }}-{{ item.endTime.substring(0, 4) }} -->
              </view>
              <wd-icon color="#333333" name="chevron-right" size="18px"></wd-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goWorkExperience">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">工作经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon class="p-l-20rpx" color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>
        <view
          v-if="onlineObj.workExperiencesList && onlineObj.workExperiencesList.length > 0"
          class="mt-10rpx"
        >
          <view
            v-for="(item, index) in onlineObj.workExperiencesList"
            :key="index"
            class="work-qw-content mt-20rpx"
            @click="editWork(item)"
          >
            <view class="work-qw-title flex-between">
              <view class="flex-c">
                <view class="text-30rpx c-#000 font-w-500">{{ item.company }}</view>
                <!-- <view class="m-l-20rpx">{{}}-13000</view> -->
              </view>
              <wd-icon color="#333333" name="chevron-right" size="18px"></wd-icon>
            </view>
            <view class="flex-between work-qw-line">
              <view class="c-#333333 text-28rpx industry-row">
                <view v-if="item.industry">
                  {{ item.industry === '不限' ? '行业不限' : item.industry }}
                </view>
                <view>{{ item.positionName }}</view>
              </view>
              <view class="c-#333333 text-28rpx">
                {{ item.startTime.slice(0, 7) }}至{{ item.endTime.slice(0, 7) }}
              </view>
            </view>
            <view
              v-if="item?.workDescription && item.workDescription.trim() !== ''"
              class="m-t-4rpx"
            >
              <view class="text-container">
                <view class="text-pre-wrap c-#333333 text-26rpx m-l-[-6rpx] work-content-text">
                  工作内容：{{ getWorkDescriptionText(item, index) }}
                  <text
                    v-if="shouldShowWorkMore(item, index)"
                    class="view-detail text-26rpx"
                    style="color: #589bff"
                    @click.stop="showWorkContentDetail(index)"
                  >
                    展开
                  </text>
                  <text
                    v-if="shouldShowWorkCollapse(item, index)"
                    class="view-detail text-26rpx"
                    style="color: #589bff"
                    @click.stop="showWorkContentShrink(index)"
                  >
                    收起
                  </text>
                </view>
              </view>
            </view>

            <view
              v-if="item?.workPerformance && item.workPerformance.trim() !== ''"
              class="m-t-4rpx"
            >
              <view class="text-container">
                <view class="text-pre-wrap c-#333333 text-26rpx m-l-[-6rpx] work-performance-text">
                  工作业绩：{{ getWorkAchievementText(item, index) }}
                  <text
                    v-if="shouldShowWorkAchievementMore(item, index)"
                    class="view-detail text-26rpx"
                    style="color: #589bff"
                    @click.stop="showWorkAchievementDetail(index)"
                  >
                    展开
                  </text>
                  <text
                    v-if="shouldShowWorkAchievementCollapse(item, index)"
                    class="view-detail text-26rpx"
                    style="color: #589bff"
                    @click.stop="showWorkAchievementShrink(index)"
                  >
                    收起
                  </text>
                </view>
              </view>
            </view>
            <!-- <view class="wx-tag">文案編輯</view> -->
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goProjectExperience">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">项目经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon class="p-l-20rpx" color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>

        <view v-if="onlineObj.projectList && onlineObj.projectList.length > 0" class="mt-10rpx">
          <view
            v-for="(item, index) in onlineObj.projectList"
            :key="index"
            class="work-qw-content mt-20rpx"
            @click="editProject(item)"
          >
            <view class="work-qw-title flex-between">
              <view class="flex-c">
                <view class="text-30rpx c-#000 font-w-500">{{ item.projectName }}</view>
              </view>
              <wd-icon color="#333333" name="chevron-right" size="18px"></wd-icon>
            </view>
            <view class="flex-between work-qw-line text-28rpx">
              <view class="c-#333333 text-28rpx">{{ item.takeOffice }}</view>
              <view class="c-#333333 text-28rpx">
                {{ item.startDate.slice(0, 7) }}至{{ item.endDate.slice(0, 7) }}
              </view>
            </view>

            <view class="text-container">
              <view class="text-pre-wrap c-#333333 text-26rpx m-l-[-6rpx] project-content-text">
                内容：{{ getProjectDescText(item, index) }}
                <text
                  v-if="shouldShowProjectMore(item, index)"
                  class="view-detail text-26rpx"
                  style="color: #589bff"
                  @click.stop="showProjectDetail(index)"
                >
                  展开
                </text>
                <text
                  v-if="shouldShowProjectCollapse(item, index)"
                  class="view-detail text-26rpx"
                  style="color: #589bff"
                  @click.stop="showProjectShrink(index)"
                >
                  收起
                </text>
              </view>
            </view>

            <view v-if="item?.projectLinkAddr && item?.projectLinkAddr.trim() !== ''">
              <view class="c-#333333 text-26rpx">
                {{ `项目链接：${item.projectLinkAddr}` }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goCertificate">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">资格证书</view>
            <wd-tooltip content="添加相关资格证书，提升竞争优势！" placement="right-start">
              <wd-icon color="#000000" name="help-circle" size="18px"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right">
            <wd-icon class="p-l-10rpx" color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>
        <view
          v-if="onlineObj.resumeCertificateVOList && onlineObj.resumeCertificateVOList.length > 0"
          class="qualification-list flex-c"
        >
          <view
            v-for="(item, index) in onlineObj.resumeCertificateVOList"
            :key="index"
            class="qualification-tag"
            @click="goCertificate"
          >
            <!-- <image :src="item.pic" mode=""></image> -->
            <view class="qualification-tag-name">
              {{ item.certificate }}
            </view>
          </view>
        </view>
      </view>
      <view class="resume-list">
        <view class="jobExpectations-title flex-between" @click="goSkill">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">掌握技能</view>
            <wd-tooltip content="填写掌握技能，突出核心竞争力！" placement="right-start">
              <wd-icon color="#000000" name="help-circle" size="18px"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right">
            <wd-icon color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>

        <view
          v-if="onlineObj.skills && onlineObj.skills.length > 0"
          class="qualification-list flex-c"
        >
          <view
            v-for="(item, index) in onlineObj.skills"
            :key="index"
            class="qualification-tag"
            @click="editSkill(onlineObj.skills)"
          >
            <view class="qualification-tag-name">
              {{ item }}
            </view>
          </view>
        </view>
      </view>
      <view class="Portfolio">
        <view
          class="jobExpectations-title flex-between"
          @click="goPortfolio(onlineObj.resumeFileVOList)"
        >
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">附件/作品集</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon color="#000000" name="add-circle1" size="40rpx"></wd-icon>
          </view>
        </view>
        <view class="Portfolio-subtitle onlineRes-subtitle">不得大于5MB，PDF格式</view>
        <view class="Portfolio-input">
          <wd-input v-if="url" v-model="url" no-border placeholder="作品集链接" readonly />
        </view>

        <view v-if="url" class="Portfolio-upload" @click="goPortfolio(onlineObj.resumeFileVOList)">
          <image class="Portfolio-upload-img" src="/static/img/zuimeizuopinji_1.png"></image>
          <view class="Portfolio-text onlineRes-subtitle p-t-10rpx">
            上传附件/作品集「不得大于5MB」
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>
<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryFullData, updateStatus } from '@/interPost/resume'
import { useResumeStore, useLoginStore } from '@/store'
import { useQueue } from 'wot-design-uni'
import { numberTokw } from '@/utils/common'
import icons from '@/resumeRelated/img/icons.png'
import birthdayIcon from '@/resumeRelated/img/birthday_icon.png'
import educationIcon from '@/resumeRelated/img/education_icon.png'
import { DICT_IDS } from '@/enum'

const loginStore = useLoginStore()
const resumeStore = useResumeStore()
const { closeOutside } = useQueue()
const { getDictData } = useDictionary()
const { getDictLabel } = useDictionary()
const onlineObj = ref<AnyObject>({})
const jobIntentionListObj = ref<AnyObject>({})
const educationsListObj = ref<AnyObject>({})
const textState1 = ref(false)
const url = ref('')
// 求职期望
const seekObj = ref({})
// 个人优势文本处理相关变量
const myLightsText = ref('') // 显示的个人优势文本
const myLightsOriginal = ref('') // 原始个人优势文本
const showMyLightsMore = ref(false) // 是否显示"展开"按钮
const showMyLightsCollapse = ref(false) // 是否显示"收起"按钮
const myLightsExpanded = ref(false) // 是否已展开

// 工作内容和业绩分别独立展开/收起状态
const workContentExpandedStates = ref<Record<number, boolean>>({}) // 工作内容展开状态
const workAchievementExpandedStates = ref<Record<number, boolean>>({}) // 工作业绩展开状态
const projectExpandedStates = ref<Record<number, boolean>>({}) // 项目经历展开状态

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
onLoad(async () => {
  const res: any = await getDictData(DICT_IDS.JOB_SEEKSTATE)
  const expressiondata = res || res.data
  qzList.value = Object.entries(expressiondata).map(([key, value]) => ({
    value: key,
    label: value,
  }))
})
onShow(() => {
  // 获取简历
  getList()
})
const qzList = ref([])
const goCheackInfo = () => {
  const idCard = ''
  const trueName = onlineObj.value.userName
  uni.navigateTo({
    url: `/setting/identityAuth/index?idCard=${idCard}&trueName=${trueName}`,
  })
}

// 获取简历信息
const getList = async () => {
  await uni.$onLaunched
  const res: any = await queryFullData()
  if (res.code === 0) {
    if (
      Array.isArray(res.data.skills) &&
      res.data.skills.length === 1 &&
      res.data.skills[0] === ''
    ) {
      res.data.skills = []
    }
    url.value = res.data.resumeFileVOList.length > 0 ? res.data.resumeFileVOList[0].url : ''
    res.data.jobIntentionList.forEach((ele: any) => {
      ele.salaryExpectationStart =
        ele.salaryExpectationStart === 0 ? '面议' : numberTokw(ele.salaryExpectationStart + '')
      ele.salaryExpectationEnd =
        ele.salaryExpectationEnd === 0 ? '' : numberTokw(ele.salaryExpectationEnd + '')
      if (ele.distanceMeters)
        ele.distanceMeters = Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
    })
    res.data.xueLi = await getDictLabel(DICT_IDS.EDUCATION_REQUIREMENT_C, res.data.xueLi)
    await Promise.all(
      res.data.jobIntentionList.map(async (ele: any) => {
        ele.jobTypeLabel = await getDictLabel(DICT_IDS.JOB_EXPECTATIONS_PROVINCE, ele.jobType)
      }),
    )
    await Promise.all(
      res.data.educationsList.map(async (ele: any) => {
        ele.qualificationLabel = await getDictLabel(
          DICT_IDS.EDUCATION_REQUIREMENT_C,
          ele.qualification,
        )
      }),
    )
    // console.log(res.data, '1234567890')
    if (!res.data?.headImgUrl) {
      res.data.headImgUrl =
        res.data.sex === 1 ? '/static/header/jobhunting1.png' : '/static/header/jobhunting2.png'
    }
    onlineObj.value = res.data
    jobIntentionListObj.value = res.data.jobIntentionList
    educationsListObj.value = res.data.educationsList

    // 处理个人优势文本
    if (res.data.myLights) {
      myLightsOriginal.value = res.data.myLights
      checkMyLightsTextLength()
    }

    // 初始化展开状态
    if (res.data.workExperiencesList) {
      workContentExpandedStates.value = {}
      workAchievementExpandedStates.value = {}
      res.data.workExperiencesList.forEach((_, index) => {
        workContentExpandedStates.value[index] = false
        workAchievementExpandedStates.value[index] = false
      })
    }
    if (res.data.projectList) {
      projectExpandedStates.value = {}
      res.data.projectList.forEach((_, index) => {
        projectExpandedStates.value[index] = false
      })
    }
  }
}
const submitJob = async (e: any) => {
  const res: any = await updateStatus({ id: onlineObj.value.id, seekStatus: e.selectedItems.value })

  if (res.code === 1) {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 微信
const goWxUpdata = () => {
  uni.navigateTo({
    url: '/setting/wxUpdata/index',
  })
}

// 上传作品集
const goPortfolio = (item: any) => {
  const list = onlineObj.value.resumeFileVOList?.length > 0 ? 'edit' : 'add'
  if (list === 'add') {
    uni.navigateTo({
      url: `/resumeRelated/onlineResume/portfolio/index?id=${onlineObj.value.id}&isAdd=${list}`,
    })
  } else {
    uni.navigateTo({
      url: `/resumeRelated/onlineResume/portfolio/index?id=${onlineObj.value.id}&isAdd=${list}`,
    })
  }
}
// 新增个人优势
const goMyAdvantage = () => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/myAdvantage/index?id=${onlineObj.value.id}&isAdd=add&myLights=${onlineObj.value.myLights}`,
  })
}
// 编辑个人优势
const goEditMyAdvantage = (obj: string) => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/myAdvantage/index?id=${onlineObj.value.id}&isAdd=add&myLights=${obj}`,
  })
}

// 检查个人优势文本长度并设置显示状态
const checkMyLightsTextLength = () => {
  if (!myLightsOriginal.value) {
    myLightsText.value = ''
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
    return
  }

  // 如果原始文本长度大于95字符
  if (myLightsOriginal.value.length > 40) {
    if (!myLightsExpanded.value) {
      // 默认显示简洁内容（前95个字符 + ...）
      myLightsText.value = myLightsOriginal.value.substring(0, 40) + '...'
      showMyLightsMore.value = true // 显示"展开"按钮
      showMyLightsCollapse.value = false // 隐藏"收起"按钮
    } else {
      // 显示完整内容
      myLightsText.value = myLightsOriginal.value
      showMyLightsMore.value = false // 隐藏"展开"按钮
      showMyLightsCollapse.value = true // 显示"收起"按钮
    }
  } else {
    // 文本长度不超过95字符，直接显示全部内容，不显示任何按钮
    myLightsText.value = myLightsOriginal.value
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
  }
}

// 显示个人优势详情
const showMyLightsDetail = () => {
  if (showMyLightsMore.value) {
    myLightsExpanded.value = true
    checkMyLightsTextLength()
  }
}

// 收起个人优势
const showMyLightsShrink = () => {
  if (showMyLightsCollapse.value) {
    myLightsExpanded.value = false
    checkMyLightsTextLength()
  }
}

// 工作内容相关方法
const getWorkDescriptionText = (item: any, index: number) => {
  if (!item.workDescription) return ''
  const isExpanded = workContentExpandedStates.value[index] || false
  if (item.workDescription.length > 41 && !isExpanded) {
    return item.workDescription.substring(0, 41) + '...'
  }
  return item.workDescription
}
const shouldShowWorkMore = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 41) return false
  return !workContentExpandedStates.value[index]
}
const shouldShowWorkCollapse = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 41) return false
  return workContentExpandedStates.value[index]
}
const showWorkContentDetail = (index: number) => {
  workContentExpandedStates.value[index] = true
}
const showWorkContentShrink = (index: number) => {
  workContentExpandedStates.value[index] = false
}
// 工作业绩相关方法
const getWorkAchievementText = (item: any, index: number) => {
  if (!item.workPerformance) return ''
  const isExpanded = workAchievementExpandedStates.value[index] || false
  if (item.workPerformance.length > 41 && !isExpanded) {
    return item.workPerformance.substring(0, 41) + '...'
  }
  return item.workPerformance
}
const shouldShowWorkAchievementMore = (item: any, index: number) => {
  if (!item.workPerformance || item.workPerformance.length <= 41) return false
  return !workAchievementExpandedStates.value[index]
}
const shouldShowWorkAchievementCollapse = (item: any, index: number) => {
  if (!item.workPerformance || item.workPerformance.length <= 41) return false
  return workAchievementExpandedStates.value[index]
}
const showWorkAchievementDetail = (index: number) => {
  workAchievementExpandedStates.value[index] = true
}
const showWorkAchievementShrink = (index: number) => {
  workAchievementExpandedStates.value[index] = false
}

// 项目经历相关方法
const getProjectDescText = (item: any, index: number) => {
  if (!item.projectDescs) return ''

  const isExpanded = projectExpandedStates.value[index] || false
  if (item.projectDescs.length > 41 && !isExpanded) {
    return item.projectDescs.substring(0, 41) + '...'
  }
  return item.projectDescs
}

const shouldShowProjectMore = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 41) return false
  return !projectExpandedStates.value[index]
}

const shouldShowProjectCollapse = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 41) return false
  return projectExpandedStates.value[index]
}

const showProjectDetail = (index: number) => {
  projectExpandedStates.value[index] = true
}

const showProjectShrink = (index: number) => {
  projectExpandedStates.value[index] = false
}
// 新增工作经历
const goWorkExperience = (item: any) => {
  if (onlineObj.value.workExperiencesList && onlineObj.value.workExperiencesList.length >= 20) {
    uni.showToast({
      title: '工作经历最多设置20个，建议修改或删除其它工作经历',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  resumeStore.setisAdd('add')
  resumeStore.setDepartment('')
  resumeStore.setCompany('')
  resumeStore.setWorkDescription('')
  resumeStore.setCompanyId('')
  resumeStore.setSkills('')
  resumeStore.setWorkPerformance('')
  loginStore.setindustryObj({})
  // 清理岗位数据
  loginStore.setpositionData({})
  uni.navigateTo({
    url: `/resumeRelated/workExperience/index?id=${onlineObj.value.id}`,
  })
}
// 编辑工作经历
const editWork = (item) => {
  console.log(item, 'item')
  resumeStore.setisAdd('edit')
  try {
    const obj = {
      industryCode: Number(item.industryId),
      industryName: item.industry,
    }
    loginStore.setindustryObj(obj)
    resumeStore.setDepartment(item.department)
    resumeStore.setCompany(item.company)
    resumeStore.setWorkDescription(item.workDescription)
    resumeStore.setCompanyId(item.companyId)
    resumeStore.setSkills(item.skills)
    resumeStore.setWorkPerformance(item.workPerformance)
    // 清理岗位数据
    loginStore.setpositionData({
      expectedPositions: item.positionName,
      expectedPositionsCode: Number(item.positionCode),
    })
  } catch (error) {}
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/workExperience/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}`,
  })
}
// 项目经历
const goProjectExperience = () => {
  if (onlineObj.value.projectList && onlineObj.value.projectList.length >= 20) {
    uni.showToast({
      title: '项目经历最多设置20个，建议修改或删除其它项目经历',
      icon: 'none',
      duration: 2000,
    })
  }
  resumeStore.setProjectDescs('')
  resumeStore.setProjectPerformance('')
  uni.navigateTo({
    url: `/resumeRelated/projectExperience/index?id=${onlineObj.value.id}&isAdd=add`,
  })
}
// 编辑项目经历
const editProject = async (item) => {
  resumeStore.setProjectDescs(item.projectDescs)
  resumeStore.setProjectPerformance(item.projectPerformance)
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/projectExperience/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
// 技能证书
const goSkill = () => {
  const str = JSON.stringify(onlineObj.value.skills)
  uni.navigateTo({
    url: `/resumeRelated/mySkill/index?id=${onlineObj.value.id}&isAdd=add&item=${encodeURIComponent(str)}`,
  })
}
// 编辑
const editSkill = (item: any) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/mySkill/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
const goCertificate = () => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/qualiCertificate/index?id=${onlineObj.value.id}`,
  })
}
// 处理添加求职期望
const handleAddJobExpectation = () => {
  if (onlineObj.value.jobIntentionList && onlineObj.value.jobIntentionList.length >= 3) {
    uni.showToast({
      title: '求职期望最多设置3个，建议修改或删除其它求职期望',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  goSeekEmployment(onlineObj.value.jobIntentionList)
}

// 求职期望
const goSeekEmployment = (item: any) => {
  console.log(item, 'item')
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?id=${onlineObj.value.id}&isAdd=add`,
  })
  loginStore.setindustryObj({
    industryName: '不限',
    industryCode: 0,
  })
}
// 编辑求职期望
const goEditSeekEmployment = (item: any) => {
  const str = JSON.stringify(item)
  try {
    const obj = {
      industryCode: Number(item.expectedIndustryCode),
      industryName: item.expectedIndustry,
    }
    loginStore.setindustryObj(obj)
  } catch (error) {}
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
// 新增简历
const goEducation = () => {
  if (onlineObj.value.educationList && onlineObj.value.educationList.length >= 5) {
    uni.showToast({
      title: '教育经历最多设置5个，建议修改或删除其它教育经历',
      icon: 'none',
      duration: 2000,
    })
  }
  resumeStore.setSchool('')
  resumeStore.setSchoolType('')
  resumeStore.setMajor('')
  uni.navigateTo({
    url: `/resumeRelated/education/index?id=${onlineObj.value.id}&isAdd=add`,
  })
}
// 编辑简历
const goEducationEdit = (item) => {
  const str = JSON.stringify(item)
  resumeStore.setSchool(item.school)
  resumeStore.setSchoolType(item.schoolType)
  resumeStore.setMajor(item.major)
  uni.navigateTo({
    url: `/resumeRelated/education/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}

// 预览
const goPreview = () => {
  uni.navigateTo({
    url: '/resumeRelated/preview/index?isPreview=' + true,
  })
}
</script>

<style lang="scss" scoped>
::v-deep .wd-picker__value {
  font-size: 28rpx;
  color: #333;
}

::v-deep .wd-tooltip__hidden {
  bottom: 100% !important;
}

::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}

::v-deep .wd-input {
  background-color: transparent !important;
}

::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-top: 4rpx !important;
  padding-bottom: 0 !important;
  padding-left: 0 !important;
  background: transparent !important;
}

::v-deep .wd-picker__arrow {
  display: none;
}

:deep(.custom-view-class) {
  .wd-cell__wrapper {
    padding: 0 !important;
  }
}

:deep(.wd-cell__value--left) {
  font-size: 28rpx !important;
}

// ::v-deep .zp-scroll-view {
//   height: 100% !important;
// }
.border-boy {
  border: 3rpx solid #3e9cff;
}

.border-griy {
  border: 3rpx solid rgba(255, 190, 190, 1);
}

.custom-class {
  padding: 20rpx 0rpx 10rpx !important;
  background: transparent !important;
}

.CustomNavBar-right {
  font-size: 28rpx;
  color: #333333;
}

.start_ICON_item {
  width: 40rpx;
  height: 40rpx;
}

.start_ICON_item-1 {
  width: 35rpx;
  height: 35rpx;
}

.onlineRes-rz {
  margin-left: 30rpx;
}

// 确保图标和文本垂直居中对齐
.wd-img {
  display: flex;
  flex-shrink: 0; // 防止图标被压缩
  align-items: center;
}

.time {
  font-size: 26rpx;
  color: #333333;
}

.name-rz {
  margin-right: 4rpx;
  font-size: 26rpx;
}

.active {
  color: #000 !important;
}

.nomal {
  color: #ff0000 !important;
}

:deep .u-input {
  height: 80rpx;
  background-color: #e3e3e3 !important;
  border-radius: 20rpx !important;
}

.onlineRes-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.onlineRes-subtitle {
  font-size: 26rpx;
  color: #666666;
}

.work-qw-line {
  line-height: 40rpx;

  .industry-row {
    display: flex;
    gap: 20rpx;
    align-items: center;
  }
}

.my-lights-text {
  display: block;
  line-height: 1.2 !important;
  text-indent: 0;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.my-lights-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.work-content-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.work-performance-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.project-content-text .view-detail {
  display: inline;
  margin-left: 0;
  white-space: nowrap;
}

.onlineRes {
  padding: 40rpx;

  .onlineRes-list {
    padding-bottom: 36rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-left {
      width: 75%;

      .onlineRes-info {
        .name {
          font-size: 42rpx;
          font-weight: bold;
          color: #000000;
        }

        .onlineRes-rz {
          margin-left: 50rpx;

          .name-rz {
            margin-right: 4rpx;
            font-size: 26rpx;
            color: #ff0000;
          }
        }
      }

      .onlineRes-my {
        font-size: 26rpx;
        line-height: 50rpx;
        color: #333333;

        .onlineRes-my-item {
          display: flex;
          gap: 4rpx;
          align-items: center;
          margin-right: 20rpx;

          text {
            display: flex;
            align-items: center;
            line-height: 1.2; // 设置合适的行高
          }
        }
      }

      .onlineRes-connect {
        .m-right {
          margin-right: 50rpx;
        }

        .onlineRes-connect-img {
          width: 28rpx !important;
          height: 28rpx !important;
        }

        .onlineRes-connect-img-1 {
          width: 30rpx;
          height: 26rpx;
        }

        .onlineRes-connect-name {
          padding-left: 5rpx;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }

    .onlineRes-right {
      width: 25%;
      margin: 0 0 0 auto;
    }
  }

  .onlineRes-job-left {
    .onlineRes-time {
      padding-top: 10rpx;
      font-size: 28rpx;
      color: #333333;
    }
  }

  //.my-lights {
  //  padding: 20rpx 0rpx 20rpx;
  //  border-bottom: 1rpx solid #d7d6d6;
  //}

  .resume-list {
    padding: 42rpx 0;
    border-bottom: 1rpx solid #d7d6d6;

    .jobExpectations-qw {
      line-height: 60rpx;
    }

    .jobExpectations-exepress-left {
      .text-name {
        line-height: 60rpx;
      }

      .text-salary {
        padding-left: 20rpx;
      }

      .text-position {
        font-size: 28rpx;
        color: #333333;
      }

      .text-denery {
        padding-left: 40rpx;
      }
    }

    .work-qw {
      padding: 20rpx 0 10rpx 0;
      border-bottom: 1rpx solid #d7d6d6;
    }

    .jobExpectations-exepress {
      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 28rpx;
          color: #333333;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }

    .work-qw-title {
      line-height: 40rpx;
    }

    .wx-tag {
      width: 160rpx;
      padding: 2rpx 5rpx;
      font-size: 24rpx;
      color: #333333;
      text-align: center;
      background-color: #e5e5e5;
      border-radius: 10rpx;
    }
  }

  .education {
    padding-top: 20rpx;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .education-list {
      padding: 0rpx 0rpx 30rpx;

      .education-left {
        width: 70%;

        .education-left-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          background-image: url('@/static/img/Group_1171274967.png');
          background-position: 100% 100%;
          background-size: 100% 100%;
        }

        .education-left-xl {
          .education-left-xl-subname {
            color: #333333;
          }
        }
      }

      .education-right {
        width: 30%;
      }
    }
  }

  .qualification-list {
    flex-wrap: wrap !important;
    width: 100%;
    padding-top: 20rpx;

    .qualification-tag {
      // width: 33%;
      padding-top: 10rpx;
      text-align: center;

      .qualification-tag-name {
        padding: 10rpx 20rpx;
        margin-right: 10rpx;
        font-size: 28rpx;
        color: #333333;
        background-color: #d9d9d9;
        border-radius: 10rpx;
      }
    }
  }

  .Portfolio {
    padding: 32rpx 0 0;

    .Portfolio-subtitle {
      padding-bottom: 40rpx;
    }

    .Portfolio-input {
      padding-bottom: 40rpx;
    }

    .Portfolio-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .Portfolio-upload-img {
        width: 120rpx;
        height: 120rpx;
      }
    }
  }

  .text-container {
    display: inline;

    .view-detail {
      display: inline;
      margin-left: 10rpx;
      color: #457ae6;
      white-space: nowrap;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .edit-link {
      color: #457ae6;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  :deep(.wd-tooltip__pos) {
    min-width: 300rpx !important;
  }

  :deep(.wd-tooltip__inner) {
    padding-right: 18rpx;
    font-size: 22rpx;
    text-align: left;
    white-space: normal;
    direction: ltr;
  }

  .work-content-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .work-performance-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .project-content-text {
    display: block;
    padding-left: 6rpx;
    line-height: 1.2 !important;
    text-indent: -6rpx;
    word-wrap: break-word;
    white-space: pre-wrap;
  }

  .full-time {
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80rpx !important;
    height: 36rpx !important;
    padding: 0 10rpx;
    color: #000000 !important;
    border: 1rpx solid #000000;
    border-radius: 8rpx;
  }

  .full-time-other {
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80rpx !important;
    height: 36rpx !important;
    padding: 0 10rpx;
    color: #000000 !important;
    border: 1rpx solid #000000;
    border-radius: 8rpx;
  }
}

:deep(.wd-input__inner) {
  font-size: 28rpx !important;
}
</style>
