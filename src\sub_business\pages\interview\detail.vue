<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle" safe-area-inset-bottom>
    <CustomNavBar title="我的面试"></CustomNavBar>
    <view class="page-time flex items-center p-l-40rpx p-r-40rpx">
      <view class="text-100rpx font-600 c-#000">{{ item.agreeTime?.slice(5, 7) || '' }}</view>
      <view>
        <view class="text-32rpx c-#555">月</view>
        <view class="text-32rpx c-#555">{{ item.agreeTime?.slice(0, 4) || '' }}年</view>
      </view>
    </view>
    <view class="pageList m-t-10rpx">
      <interviewDetail :item="item" :toType="toType" />
    </view>
    <template #bottom>
      <view v-if="toType === 'toBusiness'">
        <view
          class="m-t-40rpx p-l-40rpx p-r-40rpx flex justify-right justify-center m-b-60rpx"
          v-if="item.status === 0 || item.status === 1"
        >
          <view
            @click="cancelInterview"
            class="text-28rpx font-500 c-#FF5C5C px-50rpx py-15rpx border-1rpx border-solid border-#FF5C5C rounded-[90rpx] m-r-60rpx"
          >
            取消面试
          </view>
          <view
            @click="goInitiate"
            class="text-28rpx font-500 c-#6B7FFF px-50rpx py-15rpx border-1rpx border-solid border-#6B7FFF rounded-[90rpx]"
          >
            修改面试
          </view>
        </view>
      </view>
      <view v-else>
        <view
          class="m-t-40rpx p-l-40rpx p-r-40rpx flex justify-center justify-right m-b-60rpx"
          v-if="item.status === 0"
        >
          <view
            @click="cancelInterviewToPerson"
            class="text-28rpx font-500 c-#FF5C5C px-50rpx py-15rpx border-1rpx border-solid border-#FF5C5C rounded-[90rpx] m-r-60rpx"
          >
            取消面试
          </view>
          <view
            @click="acceptInterview"
            class="text-28rpx font-500 c-#6B7FFF px-50rpx py-15rpx border-1rpx border-solid border-#6B7FFF rounded-[90rpx] m-r-60rpx"
          >
            接受面试
          </view>
          <view
            v-if="item.status === 1"
            @click="refuseInterview"
            class="text-28rpx font-500 c-#6B7FFF px-50rpx py-15rpx border-1rpx border-solid border-#6B7FFF rounded-[90rpx]"
          >
            拒绝面试
          </view>
        </view>
      </view>
    </template>

    <interviewDiog
      @update:show="show = $event"
      :show="show"
      :item="item"
      :rejectType="rejectType"
    />
  </z-paging>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { hrInterviewRecordDetail, queryByInterViewId } from '@/service/hrBiographical'
import { userInterviewRecord } from '@/interPost/resume'
import { getCustomBar } from '@/utils/storage'
import interviewDetail from '@/sub_business/components/interviewDetail.vue'
import { getMonthDays, formatDateDay, numberTokw } from '@/utils/common'
import interviewDiog from '@/sub_business/components/interviewDiog.vue'

const { defaultRoleLogo } = useDefault()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const show = ref(false)
const toType = ref('')
const id = ref(null)
const item = ref<AnyObject>({})
const customBar = ref(null)
const rejectType = ref('')
// b取消面试
const cancelInterview = () => {
  console.log('=== cancelInterview 被调用 ===')
  show.value = true
  rejectType.value = 'cancelToBusiness'
  console.log('设置 rejectType.value =', rejectType.value)
}
// c取消面试
const cancelInterviewToPerson = () => {
  console.log('=== cancelInterviewToPerson 被调用 ===')
  show.value = true
  rejectType.value = 'cancelToPerson'

  console.log('设置 rejectType.value =', rejectType.value)
}
// 拒绝面试
const refuseInterview = () => {
  console.log('=== refuseInterview 被调用 ===')
  rejectType.value = 'refuseToPerson'
  show.value = true
  console.log('设置 rejectType.value =', rejectType.value)
}
// 修改面试
const goInitiate = () => {
  uni.navigateTo({
    url: '/sub_business/pages/interview/Initiate?id=' + item.value.id,
  })
}
// 接受面试
const acceptInterview = async () => {
  await userInterviewRecord({
    id: item.value?.id,
    status: '1',
  })
  uni.$emit('refreshInterviewListToPerson')
  uni.$emit('refreshInterview')
  uni.$emit('refreshInterviewList')
}
const queryList = async () => {
  const res: any =
    toType.value === 'toBusiness'
      ? await hrInterviewRecordDetail({
          id: id.value,
        })
      : await queryByInterViewId({
          id: id.value,
        })
  if (res.code === 0) {
    item.value = res.data
    item.value.salaryStart =
      item.value.salaryStart === 0 ? '面议' : numberTokw(item.value.salaryStart + '')
    item.value.salaryEnd = item.value.salaryEnd === 0 ? '' : numberTokw(item.value.salaryEnd + '')
    item.value.userUrl = defaultRoleLogo(item.value.sex, item.value.userUrl)
    console.log(item.value, 'item.value==========')
  }
}
uni.$on('refreshInterview', async () => {
  await queryList()
})
onUnload(() => {
  uni.$off('refreshInterview')
})
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  toType.value = options.toType
  console.log(options, 'options==========')
  id.value = options.id
  queryList()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.date-selected {
  min-width: 80rpx !important;
  padding: 0rpx 10rpx;
  color: #fff !important;
  background: #ff7648 !important;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
:deep(.custom-class) {
  height: 300rpx !important;
}
:deep(.custom-textarea-container-class) {
  height: 100% !important;
}
</style>
