<template>
  <view class="pageList-item">
    <view class="shadow-[0px_0px_14px_0px_rgba(0,0,0,0.13)] rounded-[16rpx] bg-#fff">
      <view
        class="pageList-item-right-card relative"
        :class="item.status === 1 ? 'bg-#4399ff' : 'bg-#F2F2F2'"
      >
        <view class="flex items-center">
          <view class="w-90rpx">
            <image
              class="w-76rpx h-76rpx rounded-full"
              :src="item.userUrl"
              mode="aspectFill"
            ></image>
          </view>
          <view class="flex-1">
            <view class="flex items-center justify-between">
              <view
                class="text-32rpx p-b-5rpx u-line-1"
                :class="item.status === 1 ? 'c-#fff' : 'c-#000'"
              >
                {{ toType === 'toBusiness' ? item.userName : item.hrPosition }}
              </view>
            </view>

            <view class="flex justify-between">
              <view
                class="text-28rpx font-400"
                @tap="previewDetail"
                :class="item.status === 1 ? 'c-#fff' : 'c-#000'"
              >
                {{
                  toType === 'toBusiness'
                    ? truncateText(item.positionName, 8)
                    : truncateText(item.companyName, 12)
                }}
              </view>
              <view class="text-28rpx" :class="item.status === 1 ? 'c-#fff' : 'c-#000'">
                {{ item.salaryStart }}{{ item.salaryEnd ? '-' + item.salaryEnd : '' }}
              </view>
            </view>
          </view>
        </view>
        <view class="absolute top-[7rpx] right-[85rpx] z-100" v-if="item.status === 1">
          <wd-img :width="15" :height="15" :src="starIcon" />
        </view>
        <view class="absolute top-[10rpx] right-[7rpx] z-100" v-if="item.status === 1">
          <wd-img :width="35" :height="35" :src="interview" />
        </view>

        <view
          v-if="item.status === 2 || item.status === 3 || item.status === 4"
          class="c-#FF7648 text-24rpx border-1rpx border-solid border-#FF7648 rounded-[10rpx] px-15rpx absolute top-[30rpx] right-[20rpx] z-1000"
        >
          {{
            item.status === 2 ? '用户拒绝面试' : item.status === 3 ? 'hr取消面试' : '用户取消面试'
          }}
        </view>
        <view v-if="item.status === 0">
          <view
            class="c-#4399FF text-24rpx border-1rpx border-solid border-#4399FF rounded-[10rpx] px-15rpx absolute top-[30rpx] right-[20rpx] z-1000"
          >
            待处理
          </view>
        </view>
      </view>
      <view class="px-40rpx p-b-40rpx">
        <view
          class="flex items-center justify-between border-b-1rpx border-b-solid border-b-[#DFDFDF] p-b-40rpx p-t-20rpx"
        >
          <view class="text-28rpx c-#333">面试时间：</view>
          <view class="text-28rpx c-#333 font-500">{{ item.agreeTime?.slice(0, 16) || '' }}</view>
        </view>
        <view
          class="flex items-center justify-between border-b-1rpx border-b-solid border-b-[#DFDFDF] p-b-40rpx p-t-40rpx"
        >
          <view class="text-28rpx c-#333">联系人：</view>
          <view class="flex items-center" @click.stop="callPhoneInterview(item.hrPhone)">
            <view class="text-28rpx c-#333 font-500">
              {{ item.hrName }}&nbsp;{{ item.hrPhone }}
            </view>
            <wd-img :width="18" :height="18" :src="phoneIcon" />
          </view>
        </view>
        <view
          class="flex items-center justify-between border-b-1rpx border-b-solid border-b-[#DFDFDF] p-b-40rpx p-t-40rpx"
        >
          <view class="text-28rpx c-#333 w-150rpx">地址：</view>
          <view
            class="text-28rpx c-#333 font-500 text-right flex-1"
            @click="goMap('', '', item.address)"
          >
            {{ item.address }}
          </view>
        </view>
      </view>
    </view>
    <view
      v-if="item.remark"
      class="shadow-[0px_0px_14px_0px_rgba(0,0,0,0.13)] rounded-[16rpx] bg-#fff m-t-40rpx p-40rpx text-28rpx c-#333"
    >
      {{ item.remark }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { handleNavigation } from '@/utils/open-map-url'
import interview from '@/sub_business/static/interview/interview.png'
import starIcon from '@/sub_business/static/interview/star_icon.png'
import phoneIcon from '@/sub_business/static/interview/phone_icon.png'
import { truncateText } from '@/utils/util'
import { CommonUtil } from 'wot-design-uni'
const { callPhone } = usePhoneVersion()
const props = defineProps<{
  item: any
  toType: string
}>()
const callPhoneInterview = (phone: string) => {
  console.log('点击打电话，电话号码:', phone)
  try {
    callPhone(phone)
  } catch (error) {
    console.error('打电话失败:', error)
    uni.showToast({
      title: '打电话失败',
      icon: 'none',
    })
  }
}

// 跳转详情页
const previewDetail = () => {
  if (props.toType === 'toBusiness') {
    const hrDetailItem = JSON.stringify({
      userId: props.item.receiveUserId,
    })
    uni.navigateTo({
      url: CommonUtil.buildUrlWithParams('/resumeRelated/preview/index', {
        hrDetailItem,
      }),
    })
  } else {
    uni.navigateTo({
      url: `/resumeRelated/jobDetail/index?id=${props.item.positionId}`,
    })
  }
}
const goMap = (lat, lon, adress) => {
  handleNavigation({
    latitude: lat,
    longitude: lon,
    name: adress,
  })
}
</script>

<style lang="scss" scoped>
.pageList-item-right-card {
  width: 100%;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
}

.text-28rpx {
  transition: all 0.3s ease;
}
</style>
